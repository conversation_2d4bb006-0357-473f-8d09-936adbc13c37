// /server/api/contacts.ts
import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON> } from "h3";
import { stringify } from "qs";
import { getBaseUrl } from "@/utils/dataUtils";
const { FISHBUSH_API_KEY } = useRuntimeConfig();

export default defineEventHandler(async (event) => {
	const baseUrl = getBaseUrl()

	const fullUrl = new URL("api/web-contact-groups", baseUrl);

  // Get query parameters from the incoming request
  const incomingQuery = getQuery(event);

  // Extract the locale from the query parameters, default to "cs" if not provided
  const locale = incomingQuery.locale || "cs";

	// Set default query parameters
	const queryParams = {
		fields: ["name", "weight"],
		populate: {
			webContacts: {
				fields: ["roleName", "weight"],
				populate: {
					user: {
						fields: ["firstname", "lastname", "email", "phoneNumber"],
						populate: {
							profilePictures: {
								filters: {
									projectType: {
										id: { $in: [1, 4] }, //TODO: Make dynamic
									},
								},
								fields: ["x", "y", "width", "height"],
								populate: {
									projectType: {
										fields: ["id"],
									},
									image: {
										fields: "*",
									},
								},
							},
						},
					},
				},
			},
		},
		// Add any additional static query parameters here
		locale,
	};

	// Serialize query parameters
	const queryString = stringify(queryParams, { encodeValuesOnly: true });
	fullUrl.search = `?${queryString}`;

	try {
		const token = FISHBUSH_API_KEY; // Use the appropriate API key
		const response = await fetch(fullUrl.href, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			throw createError({
				statusCode: response.status,
				statusMessage: response.statusText,
			});
		}

		const data = await response.json();
		return data;
	} catch (error) {
    console.error(`[${new Date().toLocaleString()}] ${error}`);
		throw createError({
			statusCode: 500,
			statusMessage: `${error}`,
		});
	}
});

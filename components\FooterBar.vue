<script setup lang="ts">
import { useFishBushStore } from "@/stores/FishBush";
import { useI18n } from 'vue-i18n';

const FBstore = useFishBushStore();
const resource = "web-menu-items";
const { locale, t } = useI18n();

const modifiers: FBQueryParams = {
  locale: locale.value,
};
const resourceKey = `${resource}-${locale.value}`;

const { data: webMenuData } = await useAsyncData(resourceKey, async () => {
	if (!FBstore.getFBdata(resource, modifiers)) {
		await FBstore.fetchFishBushData(resource, modifiers);
	}
	return FBstore.getFBdata(resource, modifiers);
});

const webMenuItems = computed(() =>
  (webMenuData.value ?? [])
    .filter((item): item is WebMenuItem => item != null)
    .filter(
      (item) =>
        item.menuType === Enum_Webmenuitem_Menutype.FooterNav &&
        item.isValid,
    )
    .sort((a, b) => (a.weight ?? 0) - (b.weight ?? 0)),
);
</script>

<template>
  <!-- Footer -->
  <footer class="text-sm">
    <div class="bg-gradient-to-r from-tda-200 to-tda-300">
      <!-- <button @click="setLocale('cs')">cs</button>
      <button @click="setLocale('en')">en</button> -->
      <div class="content-container flex flex-col text-center md:text-left py-5">
        <div class="items-center flex flex-wrap">
          <div class="w-full md:w-6/12">
            <span class="uppercase text-xl block mb-2">Student Cyber Games, z.s.</span>
            <p>{{ t('know_more') }}<br>
              {{ t('contact_us') }} <a class="text-informative hover:text-informative/70" href="mailto:<EMAIL>"><EMAIL></a>.</p>
          </div>
          <div class="w-full md:w-3/12 my-4 md:my-0 my">
            <a title="https://www.facebook.com/tourdeapp" href="https://www.facebook.com/tourdeapp" class="text-informative">
              <div class="logo" style="--icon-link: url('/icon_FB.svg')"></div>
            </a>
            <a title="https://www.instagram.com/tourdeapp/" href="https://www.instagram.com/tourdeapp/" class="text-informative">
              <div class="logo" style="--icon-link: url('/icon_IG.svg')"></div>
            </a>
            <a title="https://www.youtube.com/studentcybergames" href="https://www.youtube.com/studentcybergames" class="text-informative">
              <div class="logo" style="--icon-link: url('/icon_YT.svg')"></div>
            </a>
          </div>
          <!-- <hr> -->
          <div class="w-full md:w-3/12">
            <ul class="text-lg">
              <li v-for="menuItem in webMenuItems" :key="menuItem.documentId">
                <a :href="menuItem.link" class="text-informative hover:text-informative/70">{{ menuItem.text }}</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="overflow-hidden bg-black/[.2]">
        <div class="footer-copyright text-center py-3 items-center text-informative/60">© 2024 Student Cyber Games</div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.nav-item {
  @apply pl-4 text-xl lg:px-3;
}

.nav-link {
  @apply text-white transition-all duration-300 ease-[ease] hover:text-opacity-75 max-lg:px-[6px] block py-5;
}

.logo {
  @apply inline-block w-6 h-6 bg-informative mr-4 hover:bg-informative/70;
  -webkit-mask: var(--icon-link) no-repeat center;
  mask: var(--icon-link) no-repeat center;
}
</style>

export interface FBQueryParams {
	fields?: string[] | string;
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	filters?: Record<string, any>;
	populate?: string | string[] | Record<string, FBQueryParams> | boolean;
	sort?: string | string[];
	pagination?: {
		page?: number;
		pageSize?: number;
		start?: number;
		limit?: number;
	};
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	[key: string]: any;
}

export type ContentBlock = {
	__component: string;
	publishDate: string | null;
	unpublishDate: string | null;
	content: string | null;
	
	// biome-ignore lint/suspicious/noExplicitAny: Allow for other properties
	[key: string]: any;
};
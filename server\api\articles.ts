// /server/api/articles.ts
import { createError, define<PERSON>ventHandler } from "h3";
import { stringify } from "qs";
import { processHtmlContent } from "@/utils/dataUtils";
import { getBaseUrl } from "@/utils/dataUtils";
const { FISHBUSH_API_KEY } = useRuntimeConfig();

export default defineEventHandler(async (event) => {
	const baseUrl = getBaseUrl();

	const fullUrl = new URL("api/web-pages", baseUrl);

  // Get query parameters from the incoming request
  const incomingQuery = getQuery(event);

  // Extract the locale from the query parameters, default to "cs" if not provided
  const locale = incomingQuery.locale || "cs";

	// Extract the slug from the query parameters
	const slug = incomingQuery.slug;
	const id = incomingQuery.id;
	// if slug or id is not provided, return 404
	if (!(slug && id)) {
		throw createError({
			statusCode: 404,
			statusMessage: "Not Found",
		});
	}

	// Set default query parameters
	const queryParams = {
		fields: ["slug"],
		filters: {
			slug
		},
		populate: {
			blocks: {
				on: {
					"content-blocks.article-list": {
						fields: ["id", "content"],
						filters:
						{
							id
						},
						populate: {
							categories: {
								fields: ["name"],
								populate: {
									defaultCoverImage: {
										fields: "*",
									},
									articles: {
										fields: ["title", "slug", "publishDate", "publishedAt", "unpublishDate", "anotation", "showAuthor"],
										filters: {
											publishDate: { $lte: new Date().toISOString() },
											publishedAt: { $null: false },
											$or: [
												{ unpublishDate: { $gte: new Date().toISOString() } },
												{ unpublishDate: { $null: true } },
											],
										},
										populate: {
											coverImage: {
												fields: "*",
											},
											author: {
												fields: ["firstname", "lastname"]
											}
										},
									},
								},
							}
						}
					}
				}
				
			},
		},
		// Add any additional static query parameters here
		locale,
	};

	// Serialize query parameters
	const queryString = stringify(queryParams, { encodeValuesOnly: true });
	fullUrl.search = `?${queryString}`;
	try {
		const token = FISHBUSH_API_KEY; // Use the appropriate API key
		const response = await fetch(fullUrl.href, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			throw createError({
				statusCode: response.status,
				statusMessage: response.statusText,
			});
		}

		const data = await response.json();
		
		const articles: Array<WebArticleEntity> = [];

		for (const item of data.data) {
			for (const block of item.attributes.blocks) {
				for (const category of block.categories.data) {
					// Assign default cover image to articles that don't have one
					const defaultCoverImage = category.attributes.defaultCoverImage.data;
					for (const article of category.attributes.articles.data) {

						// Remove the author if showAuthor is false
						if (article.attributes.showAuthor === false) {
							article.attributes.author = null;
						}
		
						// Assign default cover image if the article doesn't have one
						if (
							!article.attributes.coverImage ||
							!article.attributes.coverImage.data
						) {
							article.attributes.coverImage = {
								data: { ...defaultCoverImage },
							};
						}

						// Process the anotation field
						if (article.attributes.anotation) {
							article.attributes.anotation = processHtmlContent(
								article.attributes.anotation,
								locale as string
							);
						}

						articles.push(article);
					}
				}
			}
		}
		
		return articles;
	} catch (error) {
    console.error(`[${new Date().toLocaleString()}] ${error}`);
		throw createError({
			statusCode: 500,
			statusMessage: `${error}`,
		});
	}
});

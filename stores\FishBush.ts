import type { FBQueryParams } from "~/models/FishBushCustomTypes";
import type { Query as NonKebabQuery } from "@/models/types";
import { getCacheKey } from "@/utils/cacheUtils";
import { defineStore } from "pinia";
import { stringify } from "qs";
import type { KebabKeys } from "string-ts";

type Resources = keyof KebabKeys<Omit<NonKebabQuery, "__typename">>;
type Query = KebabKeys<NonKebabQuery>;

export const useFishBushStore = defineStore("fishbush", {
	state: () => ({
		data: {} as Record<string, unknown>,
		fetchPromises: {} as Record<string, Promise<unknown> | null>,
	}),
	actions: {
		setFBdata<T>(cacheKey: string, data: T) {
			this.data[cacheKey] = data;
		},

		async fetchFishBushData<TResourceName extends Resources>(
			resource: TResourceName,
			fetchQueries?: FBQueryParams,
		): Promise<Query[TResourceName] | undefined> {
			// Build the URL
			let fullUrl = `/api/${resource}`;
			if (fetchQueries) {
				const queryString = stringify(fetchQueries, { encodeValuesOnly: true });
				fullUrl += `?${queryString}`;
			}
			
			// Create a unique cache key
			const cacheKey = getCacheKey(resource, fetchQueries);

			// If a fetch operation is already in progress, return its promise
			if (this.fetchPromises[cacheKey]) {
				return this.fetchPromises[cacheKey] as Promise<Query[TResourceName]>;
			}

			// Check if data is already in the store
			if (this.data[cacheKey]) {
				return this.data[cacheKey] as Query[TResourceName];
			}

			const fetchPromise = (async () => {
				try {

					const response_json = await $fetch<Query[TResourceName]>(fullUrl);
					const FBdata = response_json as Query[TResourceName];
					this.setFBdata(cacheKey, FBdata);
					return FBdata;
				} finally {
					this.fetchPromises[cacheKey] = null; // Clear the fetch promise
				}
			})();

			// Store the fetch promise
			this.fetchPromises[cacheKey] = fetchPromise;
			return fetchPromise;
		},
	},
	getters: {
		getFBdata: (state) => {
			return <TResourceName extends Resources>(
				resource: TResourceName,
				fetchQueries?: FBQueryParams,
			): Query[TResourceName] | undefined => {
				const cacheKey = getCacheKey(resource, fetchQueries);
				return state.data[cacheKey] as Query[TResourceName] | undefined;
			};
		},
	},
});

<script setup lang="ts">
import type { FBQueryParams } from "~/models/FishBushCustomTypes";
import type { WebPageBlocksDynamicZone } from "@/models/types";
import { useFishBushStore } from "@/stores/FishBush";
import { useHead } from "@vueuse/head";
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from 'vue-i18n';
import { getPathWithoutLocale } from "@/utils/localeUtils";

const route = useRoute();
const router = useRouter();
const FBstore = useFishBushStore();
const resource = "web-pages";
const { locale, defaultLocale, t } = useI18n();

const cleanedPath = getPathWithoutLocale(route.path, locale.value, defaultLocale);

const modifiers: FBQueryParams = {
	slug: cleanedPath,
	locale: locale.value
};

const event = useRequestEvent();
const cmsPage = event?.context?.webPageData;

// Set the page title and omit '|' if there is no title
useHead(() => {
	const title = cmsPage?.title ? `${cmsPage.title} | Tour de App` : t('meta.title')
	const description = cmsPage?.metaDescription ?? t('meta.description')
	const image = t('meta.image')
	return ({
	title: title,
	meta: [
		{ name: "google-site-verification", content: "SpVcESVrPL3wu7JhdAh0LfqCrDnXfMHj7N7hpJRTeiQ" },
		{ name: "description", content: description	},
    { property: 'og:title', content: title },
    { property: 'og:description', content: description },
		{ property: 'og:image', content: image },
		{ property: 'og:site_name', content: 'Tour de App' },
		{ name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: title },
    { name: 'twitter:description', content: description },
		{ name: 'twitter:image', content: image },
		{ name: 'theme-color', content: '#EF8A17' },
	]
})});

const validBlocks = computed(() => {
	// In Strapi 5, blocks are directly accessible without .attributes
	const blocks = (cmsPage?.blocks as (WebPageBlocksDynamicZone & {
		id: number;
		__component: string;
		publishDate: string;
		unpublishDate?: string;
		content?: string;
	})[]) || [];

	// Filter blocks based on publish/unpublish dates
	const now = new Date();
	return blocks.filter(block => {
		const publishDate = block.publishDate ? new Date(block.publishDate) : null;
		const unpublishDate = block.unpublishDate ? new Date(block.unpublishDate) : null;

		// Block is valid if:
		// - No publish date OR publish date is in the past
		// - AND no unpublish date OR unpublish date is in the future
		const isPublished = !publishDate || publishDate <= now;
		const isNotUnpublished = !unpublishDate || unpublishDate > now;

		return isPublished && isNotUnpublished;
	});
});

const getMetaProps = (block: Exclude<WebPageBlocksDynamicZone, Error>) => {
	// In Strapi 5, extract all properties except content and __component as metadata
	const { content, __component, ...metaProps } = block as Record<string, unknown>;
	return metaProps;
};

const componentName = (componentString: string) => {
	const parts = componentString.split(".");
	if (parts.length > 1) {
		const nameParts = parts[1]
			.split("-")
			.map((part) => part.charAt(0).toUpperCase() + part.slice(1))
			.join("");
		return `ContentBlocks${nameParts}`;
	}
	return "";
};
</script>

<template>
  <div v-if="cmsPage">
    <div v-for="block in validBlocks" :key="block.id">
      <component :is="componentName(block.__component)" :content="block.content ?? ''" :meta="getMetaProps(block)" />
    </div>
  </div>
  <LoadingSpinner v-else />
</template>

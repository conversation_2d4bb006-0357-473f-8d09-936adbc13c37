{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --dotenv .env.local", "public": "nuxt dev --host 0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "types": "strapi-generate-types quickgen", "format": "biome check --no-errors-on-unmatched --write .", "env": "sync-dotenv --env .env"}, "dependencies": {"@fortawesome/vue-fontawesome": "^3.0.8", "@nuxt/content": "^2.13.4", "@nuxt/types": "^2.18.1", "@nuxtjs/google-gtag": "^1.0.4", "@nuxtjs/i18n": "^8.5.6", "@nuxtjs/robots": "^4.1.11", "@nuxtjs/sitemap": "^6.1.5", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.5.5", "@tailwindcss/forms": "^0.5.10", "@tour-de-app/authfish": "1.2.0", "caniuse-lite": "^1.0.30001726", "fast-fuzzy": "^1.12.0", "hono": "^4.8.4", "http-status-codes": "^2.3.0", "linkedom": "^0.18.11", "nuxt": "^3.17.6", "nuxt-gtag": "^3.0.3", "nuxt-schema-org": "^3.5.0", "nuxt-zod-i18n": "^1.12.0", "pinia": "^2.3.1", "qs": "^6.14.0", "string-ts": "^2.2.1", "uuid": "^10.0.0", "vue": "^3.5.17", "vue-router": "^4.5.1", "zod": "^3.25.73"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tailwindcss/typography": "^0.5.16", "@types/uuid": "^10.0.0", "husky": "^9.1.7", "strapi-generate-types": "^1.0.0", "tailwind-scrollbar": "^3.1.0", "typescript": "^5.8.3", "vue-tsc": "^1.8.27"}}
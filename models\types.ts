export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: any;
  /** A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar. */
  DateTime: any;
  /** A time string with format HH:mm:ss.SSS */
  Time: any;
  /** A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar. */
  Date: any;
  /** The `BigInt` scalar type represents non-fractional signed whole numeric values. */
  Long: any;
  TdaFisherFlagNoteDynamicZoneInput: any;
  WebPageBlocksDynamicZoneInput: any;
  /** A string used to identify an i18n locale */
  I18NLocaleCode: any;
};

export type Error = {
  __typename?: 'Error';
  code: Scalars['String'];
  message?: Maybe<Scalars['String']>;
};

export type Pagination = {
  __typename?: 'Pagination';
  total: Scalars['Int'];
  page: Scalars['Int'];
  pageSize: Scalars['Int'];
  pageCount: Scalars['Int'];
};

export type DeleteMutationResponse = {
  __typename?: 'DeleteMutationResponse';
  documentId: Scalars['ID'];
};

export enum PublicationStatus {
  Draft = 'DRAFT',
  Published = 'PUBLISHED'
}

export type IdFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  not?: InputMaybe<IdFilterInput>;
  eq?: InputMaybe<Scalars['ID']>;
  eqi?: InputMaybe<Scalars['ID']>;
  ne?: InputMaybe<Scalars['ID']>;
  nei?: InputMaybe<Scalars['ID']>;
  startsWith?: InputMaybe<Scalars['ID']>;
  endsWith?: InputMaybe<Scalars['ID']>;
  contains?: InputMaybe<Scalars['ID']>;
  notContains?: InputMaybe<Scalars['ID']>;
  containsi?: InputMaybe<Scalars['ID']>;
  notContainsi?: InputMaybe<Scalars['ID']>;
  gt?: InputMaybe<Scalars['ID']>;
  gte?: InputMaybe<Scalars['ID']>;
  lt?: InputMaybe<Scalars['ID']>;
  lte?: InputMaybe<Scalars['ID']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type BooleanFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  not?: InputMaybe<BooleanFilterInput>;
  eq?: InputMaybe<Scalars['Boolean']>;
  eqi?: InputMaybe<Scalars['Boolean']>;
  ne?: InputMaybe<Scalars['Boolean']>;
  nei?: InputMaybe<Scalars['Boolean']>;
  startsWith?: InputMaybe<Scalars['Boolean']>;
  endsWith?: InputMaybe<Scalars['Boolean']>;
  contains?: InputMaybe<Scalars['Boolean']>;
  notContains?: InputMaybe<Scalars['Boolean']>;
  containsi?: InputMaybe<Scalars['Boolean']>;
  notContainsi?: InputMaybe<Scalars['Boolean']>;
  gt?: InputMaybe<Scalars['Boolean']>;
  gte?: InputMaybe<Scalars['Boolean']>;
  lt?: InputMaybe<Scalars['Boolean']>;
  lte?: InputMaybe<Scalars['Boolean']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Boolean']>>>;
};

export type StringFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  not?: InputMaybe<StringFilterInput>;
  eq?: InputMaybe<Scalars['String']>;
  eqi?: InputMaybe<Scalars['String']>;
  ne?: InputMaybe<Scalars['String']>;
  nei?: InputMaybe<Scalars['String']>;
  startsWith?: InputMaybe<Scalars['String']>;
  endsWith?: InputMaybe<Scalars['String']>;
  contains?: InputMaybe<Scalars['String']>;
  notContains?: InputMaybe<Scalars['String']>;
  containsi?: InputMaybe<Scalars['String']>;
  notContainsi?: InputMaybe<Scalars['String']>;
  gt?: InputMaybe<Scalars['String']>;
  gte?: InputMaybe<Scalars['String']>;
  lt?: InputMaybe<Scalars['String']>;
  lte?: InputMaybe<Scalars['String']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type IntFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  not?: InputMaybe<IntFilterInput>;
  eq?: InputMaybe<Scalars['Int']>;
  eqi?: InputMaybe<Scalars['Int']>;
  ne?: InputMaybe<Scalars['Int']>;
  nei?: InputMaybe<Scalars['Int']>;
  startsWith?: InputMaybe<Scalars['Int']>;
  endsWith?: InputMaybe<Scalars['Int']>;
  contains?: InputMaybe<Scalars['Int']>;
  notContains?: InputMaybe<Scalars['Int']>;
  containsi?: InputMaybe<Scalars['Int']>;
  notContainsi?: InputMaybe<Scalars['Int']>;
  gt?: InputMaybe<Scalars['Int']>;
  gte?: InputMaybe<Scalars['Int']>;
  lt?: InputMaybe<Scalars['Int']>;
  lte?: InputMaybe<Scalars['Int']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
};

export type LongFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  not?: InputMaybe<LongFilterInput>;
  eq?: InputMaybe<Scalars['Long']>;
  eqi?: InputMaybe<Scalars['Long']>;
  ne?: InputMaybe<Scalars['Long']>;
  nei?: InputMaybe<Scalars['Long']>;
  startsWith?: InputMaybe<Scalars['Long']>;
  endsWith?: InputMaybe<Scalars['Long']>;
  contains?: InputMaybe<Scalars['Long']>;
  notContains?: InputMaybe<Scalars['Long']>;
  containsi?: InputMaybe<Scalars['Long']>;
  notContainsi?: InputMaybe<Scalars['Long']>;
  gt?: InputMaybe<Scalars['Long']>;
  gte?: InputMaybe<Scalars['Long']>;
  lt?: InputMaybe<Scalars['Long']>;
  lte?: InputMaybe<Scalars['Long']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Long']>>>;
};

export type FloatFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  not?: InputMaybe<FloatFilterInput>;
  eq?: InputMaybe<Scalars['Float']>;
  eqi?: InputMaybe<Scalars['Float']>;
  ne?: InputMaybe<Scalars['Float']>;
  nei?: InputMaybe<Scalars['Float']>;
  startsWith?: InputMaybe<Scalars['Float']>;
  endsWith?: InputMaybe<Scalars['Float']>;
  contains?: InputMaybe<Scalars['Float']>;
  notContains?: InputMaybe<Scalars['Float']>;
  containsi?: InputMaybe<Scalars['Float']>;
  notContainsi?: InputMaybe<Scalars['Float']>;
  gt?: InputMaybe<Scalars['Float']>;
  gte?: InputMaybe<Scalars['Float']>;
  lt?: InputMaybe<Scalars['Float']>;
  lte?: InputMaybe<Scalars['Float']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Float']>>>;
};

export type DateFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  not?: InputMaybe<DateFilterInput>;
  eq?: InputMaybe<Scalars['Date']>;
  eqi?: InputMaybe<Scalars['Date']>;
  ne?: InputMaybe<Scalars['Date']>;
  nei?: InputMaybe<Scalars['Date']>;
  startsWith?: InputMaybe<Scalars['Date']>;
  endsWith?: InputMaybe<Scalars['Date']>;
  contains?: InputMaybe<Scalars['Date']>;
  notContains?: InputMaybe<Scalars['Date']>;
  containsi?: InputMaybe<Scalars['Date']>;
  notContainsi?: InputMaybe<Scalars['Date']>;
  gt?: InputMaybe<Scalars['Date']>;
  gte?: InputMaybe<Scalars['Date']>;
  lt?: InputMaybe<Scalars['Date']>;
  lte?: InputMaybe<Scalars['Date']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Date']>>>;
};

export type TimeFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['Time']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['Time']>>>;
  not?: InputMaybe<TimeFilterInput>;
  eq?: InputMaybe<Scalars['Time']>;
  eqi?: InputMaybe<Scalars['Time']>;
  ne?: InputMaybe<Scalars['Time']>;
  nei?: InputMaybe<Scalars['Time']>;
  startsWith?: InputMaybe<Scalars['Time']>;
  endsWith?: InputMaybe<Scalars['Time']>;
  contains?: InputMaybe<Scalars['Time']>;
  notContains?: InputMaybe<Scalars['Time']>;
  containsi?: InputMaybe<Scalars['Time']>;
  notContainsi?: InputMaybe<Scalars['Time']>;
  gt?: InputMaybe<Scalars['Time']>;
  gte?: InputMaybe<Scalars['Time']>;
  lt?: InputMaybe<Scalars['Time']>;
  lte?: InputMaybe<Scalars['Time']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Time']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Time']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['Time']>>>;
};

export type DateTimeFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  not?: InputMaybe<DateTimeFilterInput>;
  eq?: InputMaybe<Scalars['DateTime']>;
  eqi?: InputMaybe<Scalars['DateTime']>;
  ne?: InputMaybe<Scalars['DateTime']>;
  nei?: InputMaybe<Scalars['DateTime']>;
  startsWith?: InputMaybe<Scalars['DateTime']>;
  endsWith?: InputMaybe<Scalars['DateTime']>;
  contains?: InputMaybe<Scalars['DateTime']>;
  notContains?: InputMaybe<Scalars['DateTime']>;
  containsi?: InputMaybe<Scalars['DateTime']>;
  notContainsi?: InputMaybe<Scalars['DateTime']>;
  gt?: InputMaybe<Scalars['DateTime']>;
  gte?: InputMaybe<Scalars['DateTime']>;
  lt?: InputMaybe<Scalars['DateTime']>;
  lte?: InputMaybe<Scalars['DateTime']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
};

export type JsonFilterInput = {
  and?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  or?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  not?: InputMaybe<JsonFilterInput>;
  eq?: InputMaybe<Scalars['JSON']>;
  eqi?: InputMaybe<Scalars['JSON']>;
  ne?: InputMaybe<Scalars['JSON']>;
  nei?: InputMaybe<Scalars['JSON']>;
  startsWith?: InputMaybe<Scalars['JSON']>;
  endsWith?: InputMaybe<Scalars['JSON']>;
  contains?: InputMaybe<Scalars['JSON']>;
  notContains?: InputMaybe<Scalars['JSON']>;
  containsi?: InputMaybe<Scalars['JSON']>;
  notContainsi?: InputMaybe<Scalars['JSON']>;
  gt?: InputMaybe<Scalars['JSON']>;
  gte?: InputMaybe<Scalars['JSON']>;
  lt?: InputMaybe<Scalars['JSON']>;
  lte?: InputMaybe<Scalars['JSON']>;
  null?: InputMaybe<Scalars['Boolean']>;
  notNull?: InputMaybe<Scalars['Boolean']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
  between?: InputMaybe<Array<InputMaybe<Scalars['JSON']>>>;
};

export type ComponentTdcDeploymentFiltersInput = {
  commit?: InputMaybe<StringFilterInput>;
  config?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdcDeploymentFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdcDeploymentFiltersInput>>>;
  not?: InputMaybe<ComponentTdcDeploymentFiltersInput>;
};

export type ComponentTdcDeploymentInput = {
  id?: InputMaybe<Scalars['ID']>;
  commit?: InputMaybe<Scalars['String']>;
  config?: InputMaybe<Scalars['String']>;
};

export type ComponentTdcDeployment = {
  __typename?: 'ComponentTdcDeployment';
  id: Scalars['ID'];
  commit?: Maybe<Scalars['String']>;
  config?: Maybe<Scalars['String']>;
};

export type ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput = {
  start?: InputMaybe<TimeFilterInput>;
  end?: InputMaybe<TimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>>>;
  not?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
};

export type ComponentTdaRezfishMeetComponentsTimeBlockInput = {
  id?: InputMaybe<Scalars['ID']>;
  start?: InputMaybe<Scalars['Time']>;
  end?: InputMaybe<Scalars['Time']>;
};

export type ComponentTdaRezfishMeetComponentsTimeBlock = {
  __typename?: 'ComponentTdaRezfishMeetComponentsTimeBlock';
  id: Scalars['ID'];
  start?: Maybe<Scalars['Time']>;
  end?: Maybe<Scalars['Time']>;
};

export type ComponentTdaRezfishMeetComponentsAvailableTimeFiltersInput = {
  monday?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  tuesday?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  wednesday?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  thursday?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  friday?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  saturday?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  sunday?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsAvailableTimeFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsAvailableTimeFiltersInput>>>;
  not?: InputMaybe<ComponentTdaRezfishMeetComponentsAvailableTimeFiltersInput>;
};

export type ComponentTdaRezfishMeetComponentsAvailableTimeInput = {
  id?: InputMaybe<Scalars['ID']>;
  monday?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockInput>>>;
  tuesday?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockInput>>>;
  wednesday?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockInput>>>;
  thursday?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockInput>>>;
  friday?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockInput>>>;
  saturday?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockInput>>>;
  sunday?: InputMaybe<Array<InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockInput>>>;
};

export type ComponentTdaRezfishMeetComponentsAvailableTime = {
  __typename?: 'ComponentTdaRezfishMeetComponentsAvailableTime';
  id: Scalars['ID'];
  monday?: Maybe<Array<Maybe<ComponentTdaRezfishMeetComponentsTimeBlock>>>;
  tuesday?: Maybe<Array<Maybe<ComponentTdaRezfishMeetComponentsTimeBlock>>>;
  wednesday?: Maybe<Array<Maybe<ComponentTdaRezfishMeetComponentsTimeBlock>>>;
  thursday?: Maybe<Array<Maybe<ComponentTdaRezfishMeetComponentsTimeBlock>>>;
  friday?: Maybe<Array<Maybe<ComponentTdaRezfishMeetComponentsTimeBlock>>>;
  saturday?: Maybe<Array<Maybe<ComponentTdaRezfishMeetComponentsTimeBlock>>>;
  sunday?: Maybe<Array<Maybe<ComponentTdaRezfishMeetComponentsTimeBlock>>>;
};


export type ComponentTdaRezfishMeetComponentsAvailableTimeMondayArgs = {
  filters?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentTdaRezfishMeetComponentsAvailableTimeTuesdayArgs = {
  filters?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentTdaRezfishMeetComponentsAvailableTimeWednesdayArgs = {
  filters?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentTdaRezfishMeetComponentsAvailableTimeThursdayArgs = {
  filters?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentTdaRezfishMeetComponentsAvailableTimeFridayArgs = {
  filters?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentTdaRezfishMeetComponentsAvailableTimeSaturdayArgs = {
  filters?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentTdaRezfishMeetComponentsAvailableTimeSundayArgs = {
  filters?: InputMaybe<ComponentTdaRezfishMeetComponentsTimeBlockFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ComponentTdaFisherPhasesResultFiltersInput = {
  tda_team?: InputMaybe<TdaCoreTeamFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesResultFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesResultFiltersInput>>>;
  not?: InputMaybe<ComponentTdaFisherPhasesResultFiltersInput>;
};

export type ComponentTdaFisherPhasesResultInput = {
  id?: InputMaybe<Scalars['ID']>;
  file?: InputMaybe<Scalars['ID']>;
  tda_team?: InputMaybe<Scalars['ID']>;
};

export type ComponentTdaFisherPhasesResult = {
  __typename?: 'ComponentTdaFisherPhasesResult';
  id: Scalars['ID'];
  file?: Maybe<UploadFile>;
  tda_team?: Maybe<TdaCoreTeam>;
};

export type ComponentTdaFisherPhasesLinkFiltersInput = {
  name?: InputMaybe<StringFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesLinkFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesLinkFiltersInput>>>;
  not?: InputMaybe<ComponentTdaFisherPhasesLinkFiltersInput>;
};

export type ComponentTdaFisherPhasesLinkInput = {
  id?: InputMaybe<Scalars['ID']>;
  name?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
};

export type ComponentTdaFisherPhasesLink = {
  __typename?: 'ComponentTdaFisherPhasesLink';
  id: Scalars['ID'];
  name: Scalars['String'];
  url: Scalars['String'];
};

export type ComponentTdaFisherPhasesInfoFiltersInput = {
  links?: InputMaybe<ComponentTdaFisherPhasesLinkFiltersInput>;
  title?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesInfoFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesInfoFiltersInput>>>;
  not?: InputMaybe<ComponentTdaFisherPhasesInfoFiltersInput>;
};

export type ComponentTdaFisherPhasesInfoInput = {
  id?: InputMaybe<Scalars['ID']>;
  links?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesLinkInput>>>;
  title?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Scalars['String']>;
};

export type ComponentTdaFisherPhasesInfo = {
  __typename?: 'ComponentTdaFisherPhasesInfo';
  id: Scalars['ID'];
  links?: Maybe<Array<Maybe<ComponentTdaFisherPhasesLink>>>;
  title?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
};


export type ComponentTdaFisherPhasesInfoLinksArgs = {
  filters?: InputMaybe<ComponentTdaFisherPhasesLinkFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ComponentTdaFisherPhasesInfoPanel = {
  __typename?: 'ComponentTdaFisherPhasesInfoPanel';
  id: Scalars['ID'];
  header: Scalars['String'];
  content: Scalars['String'];
};

export type ComponentTdaCtfComponentsStickyNote = {
  __typename?: 'ComponentTdaCtfComponentsStickyNote';
  id: Scalars['ID'];
  header?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentTdaCtfComponentsGenericNote = {
  __typename?: 'ComponentTdaCtfComponentsGenericNote';
  id: Scalars['ID'];
  content?: Maybe<Scalars['String']>;
};

export type ComponentTdaCtfComponentsEmail = {
  __typename?: 'ComponentTdaCtfComponentsEmail';
  id: Scalars['ID'];
  fromName?: Maybe<Scalars['String']>;
  fromEmail?: Maybe<Scalars['String']>;
  sentAt?: Maybe<Scalars['DateTime']>;
  subject?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentMultimediaBlocksFormCheckFieldFiltersInput = {
  content?: InputMaybe<StringFilterInput>;
  isRequired?: InputMaybe<BooleanFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>>>;
  not?: InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>;
};

export type ComponentMultimediaBlocksFormCheckField = {
  __typename?: 'ComponentMultimediaBlocksFormCheckField';
  id: Scalars['ID'];
  content: Scalars['String'];
  isRequired: Scalars['Boolean'];
  name: Scalars['String'];
};

export type ComponentMultimediaBlocksCroppedImageFiltersInput = {
  x?: InputMaybe<IntFilterInput>;
  y?: InputMaybe<IntFilterInput>;
  width?: InputMaybe<IntFilterInput>;
  height?: InputMaybe<IntFilterInput>;
  projectType?: InputMaybe<CoreProjectTypeFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>>>;
  not?: InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>;
};

export type ComponentMultimediaBlocksCroppedImageInput = {
  id?: InputMaybe<Scalars['ID']>;
  image?: InputMaybe<Scalars['ID']>;
  x?: InputMaybe<Scalars['Int']>;
  y?: InputMaybe<Scalars['Int']>;
  width?: InputMaybe<Scalars['Int']>;
  height?: InputMaybe<Scalars['Int']>;
  projectType?: InputMaybe<Scalars['ID']>;
};

export type ComponentMultimediaBlocksCroppedImage = {
  __typename?: 'ComponentMultimediaBlocksCroppedImage';
  id: Scalars['ID'];
  image: UploadFile;
  x: Scalars['Int'];
  y: Scalars['Int'];
  width: Scalars['Int'];
  height: Scalars['Int'];
  projectType?: Maybe<CoreProjectType>;
};

export type ComponentMailingListOfSentEMailsFiltersInput = {
  content?: InputMaybe<StringFilterInput>;
  recipient?: InputMaybe<CoreSchoolContactFiltersInput>;
  fromName?: InputMaybe<StringFilterInput>;
  fromMail?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentMailingListOfSentEMailsFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentMailingListOfSentEMailsFiltersInput>>>;
  not?: InputMaybe<ComponentMailingListOfSentEMailsFiltersInput>;
};

export type ComponentMailingListOfSentEMailsInput = {
  id?: InputMaybe<Scalars['ID']>;
  content?: InputMaybe<Scalars['String']>;
  recipient?: InputMaybe<Scalars['ID']>;
  fromName?: InputMaybe<Scalars['String']>;
  fromMail?: InputMaybe<Scalars['String']>;
};

export type ComponentMailingListOfSentEMails = {
  __typename?: 'ComponentMailingListOfSentEMails';
  id: Scalars['ID'];
  content: Scalars['String'];
  recipient?: Maybe<CoreSchoolContact>;
  fromName?: Maybe<Scalars['String']>;
  fromMail: Scalars['String'];
};

export type ComponentMailingFullEMailFiltersInput = {
  firstName?: InputMaybe<StringFilterInput>;
  lastName?: InputMaybe<StringFilterInput>;
  mail?: InputMaybe<StringFilterInput>;
  contact?: InputMaybe<CoreSchoolContactFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentMailingFullEMailFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentMailingFullEMailFiltersInput>>>;
  not?: InputMaybe<ComponentMailingFullEMailFiltersInput>;
};

export type ComponentMailingFullEMailInput = {
  id?: InputMaybe<Scalars['ID']>;
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  mail?: InputMaybe<Scalars['String']>;
  contact?: InputMaybe<Scalars['ID']>;
};

export type ComponentMailingFullEMail = {
  __typename?: 'ComponentMailingFullEMail';
  id: Scalars['ID'];
  firstName?: Maybe<Scalars['String']>;
  lastName?: Maybe<Scalars['String']>;
  mail?: Maybe<Scalars['String']>;
  contact?: Maybe<CoreSchoolContact>;
};

export type ComponentDiscordDiscordIdListFiltersInput = {
  discordId?: InputMaybe<StringFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ComponentDiscordDiscordIdListFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ComponentDiscordDiscordIdListFiltersInput>>>;
  not?: InputMaybe<ComponentDiscordDiscordIdListFiltersInput>;
};

export type ComponentDiscordDiscordIdListInput = {
  id?: InputMaybe<Scalars['ID']>;
  discordId?: InputMaybe<Scalars['String']>;
};

export type ComponentDiscordDiscordIdList = {
  __typename?: 'ComponentDiscordDiscordIdList';
  id: Scalars['ID'];
  discordId: Scalars['String'];
};

export type ComponentContentBlocksRegistrationSuccess = {
  __typename?: 'ComponentContentBlocksRegistrationSuccess';
  id: Scalars['ID'];
  publishDate?: Maybe<Scalars['DateTime']>;
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  refNextUrl?: Maybe<Scalars['String']>;
};

export enum Enum_Componentcontentblocksregistrationform_Eventpick {
  None = 'None',
  SameRegion = 'SameRegion',
  SameTopRegion = 'SameTopRegion',
  Any = 'Any'
}

export type ComponentContentBlocksRegistrationForm = {
  __typename?: 'ComponentContentBlocksRegistrationForm';
  id: Scalars['ID'];
  publishDate?: Maybe<Scalars['DateTime']>;
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  registrationSuccessUrl?: Maybe<Scalars['String']>;
  schoolTypes_connection?: Maybe<CoreSchoolTypeRelationResponseCollection>;
  schoolTypes: Array<Maybe<CoreSchoolType>>;
  eventPick: Enum_Componentcontentblocksregistrationform_Eventpick;
  hasReferral: Scalars['Boolean'];
  hasTeacher: Scalars['Boolean'];
  hasCaptain: Scalars['Boolean'];
  minContestants: Scalars['Int'];
  maxContestants: Scalars['Int'];
  substituteContestants: Scalars['Int'];
  checkFields?: Maybe<Array<Maybe<ComponentMultimediaBlocksFormCheckField>>>;
  hasInvoice: Scalars['Boolean'];
};


export type ComponentContentBlocksRegistrationFormSchoolTypes_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentContentBlocksRegistrationFormSchoolTypesArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentContentBlocksRegistrationFormCheckFieldsArgs = {
  filters?: InputMaybe<ComponentMultimediaBlocksFormCheckFieldFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ComponentContentBlocksPartners = {
  __typename?: 'ComponentContentBlocksPartners';
  id: Scalars['ID'];
  publishDate?: Maybe<Scalars['DateTime']>;
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksHomeSplashScreen = {
  __typename?: 'ComponentContentBlocksHomeSplashScreen';
  id: Scalars['ID'];
  publishDate?: Maybe<Scalars['DateTime']>;
  unpublishDate?: Maybe<Scalars['DateTime']>;
  picture: UploadFile;
  content?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksContacts = {
  __typename?: 'ComponentContentBlocksContacts';
  id: Scalars['ID'];
  publishDate?: Maybe<Scalars['DateTime']>;
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksBasicContent = {
  __typename?: 'ComponentContentBlocksBasicContent';
  id: Scalars['ID'];
  publishDate?: Maybe<Scalars['DateTime']>;
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
};

export type ComponentContentBlocksArticleList = {
  __typename?: 'ComponentContentBlocksArticleList';
  id: Scalars['ID'];
  publishDate?: Maybe<Scalars['DateTime']>;
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  categories_connection?: Maybe<WebArticleCategoryRelationResponseCollection>;
  categories: Array<Maybe<WebArticleCategory>>;
};


export type ComponentContentBlocksArticleListCategories_ConnectionArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ComponentContentBlocksArticleListCategoriesArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type UploadFileFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  alternativeText?: InputMaybe<StringFilterInput>;
  caption?: InputMaybe<StringFilterInput>;
  width?: InputMaybe<IntFilterInput>;
  height?: InputMaybe<IntFilterInput>;
  formats?: InputMaybe<JsonFilterInput>;
  hash?: InputMaybe<StringFilterInput>;
  ext?: InputMaybe<StringFilterInput>;
  mime?: InputMaybe<StringFilterInput>;
  size?: InputMaybe<FloatFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  previewUrl?: InputMaybe<StringFilterInput>;
  provider?: InputMaybe<StringFilterInput>;
  provider_metadata?: InputMaybe<JsonFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UploadFileFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UploadFileFiltersInput>>>;
  not?: InputMaybe<UploadFileFiltersInput>;
};

export type UploadFile = {
  __typename?: 'UploadFile';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  alternativeText?: Maybe<Scalars['String']>;
  caption?: Maybe<Scalars['String']>;
  width?: Maybe<Scalars['Int']>;
  height?: Maybe<Scalars['Int']>;
  formats?: Maybe<Scalars['JSON']>;
  hash: Scalars['String'];
  ext?: Maybe<Scalars['String']>;
  mime: Scalars['String'];
  size: Scalars['Float'];
  url: Scalars['String'];
  previewUrl?: Maybe<Scalars['String']>;
  provider: Scalars['String'];
  provider_metadata?: Maybe<Scalars['JSON']>;
  related?: Maybe<Array<Maybe<GenericMorph>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type UploadFileEntityResponseCollection = {
  __typename?: 'UploadFileEntityResponseCollection';
  nodes: Array<UploadFile>;
  pageInfo: Pagination;
};

export type I18NLocaleFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  code?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<I18NLocaleFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<I18NLocaleFiltersInput>>>;
  not?: InputMaybe<I18NLocaleFiltersInput>;
};

export type I18NLocale = {
  __typename?: 'I18NLocale';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  code?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type I18NLocaleEntityResponseCollection = {
  __typename?: 'I18NLocaleEntityResponseCollection';
  nodes: Array<I18NLocale>;
  pageInfo: Pagination;
};

export type ReviewWorkflowsWorkflowFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  stages?: InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>;
  stageRequiredToPublish?: InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>;
  contentTypes?: InputMaybe<JsonFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ReviewWorkflowsWorkflowFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ReviewWorkflowsWorkflowFiltersInput>>>;
  not?: InputMaybe<ReviewWorkflowsWorkflowFiltersInput>;
};

export type ReviewWorkflowsWorkflowInput = {
  name?: InputMaybe<Scalars['String']>;
  stages?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  stageRequiredToPublish?: InputMaybe<Scalars['ID']>;
  contentTypes?: InputMaybe<Scalars['JSON']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type ReviewWorkflowsWorkflow = {
  __typename?: 'ReviewWorkflowsWorkflow';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  stages_connection?: Maybe<ReviewWorkflowsWorkflowStageRelationResponseCollection>;
  stages: Array<Maybe<ReviewWorkflowsWorkflowStage>>;
  stageRequiredToPublish?: Maybe<ReviewWorkflowsWorkflowStage>;
  contentTypes: Scalars['JSON'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type ReviewWorkflowsWorkflowStages_ConnectionArgs = {
  filters?: InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ReviewWorkflowsWorkflowStagesArgs = {
  filters?: InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ReviewWorkflowsWorkflowEntityResponseCollection = {
  __typename?: 'ReviewWorkflowsWorkflowEntityResponseCollection';
  nodes: Array<ReviewWorkflowsWorkflow>;
  pageInfo: Pagination;
};

export type ReviewWorkflowsWorkflowStageFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  color?: InputMaybe<StringFilterInput>;
  workflow?: InputMaybe<ReviewWorkflowsWorkflowFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>>>;
  not?: InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>;
};

export type ReviewWorkflowsWorkflowStageInput = {
  name?: InputMaybe<Scalars['String']>;
  color?: InputMaybe<Scalars['String']>;
  workflow?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type ReviewWorkflowsWorkflowStage = {
  __typename?: 'ReviewWorkflowsWorkflowStage';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  color?: Maybe<Scalars['String']>;
  workflow?: Maybe<ReviewWorkflowsWorkflow>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type ReviewWorkflowsWorkflowStageEntityResponseCollection = {
  __typename?: 'ReviewWorkflowsWorkflowStageEntityResponseCollection';
  nodes: Array<ReviewWorkflowsWorkflowStage>;
  pageInfo: Pagination;
};

export type ReviewWorkflowsWorkflowStageRelationResponseCollection = {
  __typename?: 'ReviewWorkflowsWorkflowStageRelationResponseCollection';
  nodes: Array<ReviewWorkflowsWorkflowStage>;
};

export type StrapiPluginSsoRolesFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  oauth_type?: InputMaybe<StringFilterInput>;
  roles?: InputMaybe<JsonFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<StrapiPluginSsoRolesFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<StrapiPluginSsoRolesFiltersInput>>>;
  not?: InputMaybe<StrapiPluginSsoRolesFiltersInput>;
};

export type StrapiPluginSsoRolesInput = {
  oauth_type?: InputMaybe<Scalars['String']>;
  roles?: InputMaybe<Scalars['JSON']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type StrapiPluginSsoRoles = {
  __typename?: 'StrapiPluginSsoRoles';
  documentId: Scalars['ID'];
  oauth_type: Scalars['String'];
  roles?: Maybe<Scalars['JSON']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type StrapiPluginSsoRolesEntityResponseCollection = {
  __typename?: 'StrapiPluginSsoRolesEntityResponseCollection';
  nodes: Array<StrapiPluginSsoRoles>;
  pageInfo: Pagination;
};

export type StrapiPluginSsoWhitelistsFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<StrapiPluginSsoWhitelistsFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<StrapiPluginSsoWhitelistsFiltersInput>>>;
  not?: InputMaybe<StrapiPluginSsoWhitelistsFiltersInput>;
};

export type StrapiPluginSsoWhitelistsInput = {
  email?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type StrapiPluginSsoWhitelists = {
  __typename?: 'StrapiPluginSsoWhitelists';
  documentId: Scalars['ID'];
  email: Scalars['String'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type StrapiPluginSsoWhitelistsEntityResponseCollection = {
  __typename?: 'StrapiPluginSsoWhitelistsEntityResponseCollection';
  nodes: Array<StrapiPluginSsoWhitelists>;
  pageInfo: Pagination;
};

export type UsersPermissionsPermissionFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  action?: InputMaybe<StringFilterInput>;
  role?: InputMaybe<UsersPermissionsRoleFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UsersPermissionsPermissionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UsersPermissionsPermissionFiltersInput>>>;
  not?: InputMaybe<UsersPermissionsPermissionFiltersInput>;
};

export type UsersPermissionsPermission = {
  __typename?: 'UsersPermissionsPermission';
  documentId: Scalars['ID'];
  action: Scalars['String'];
  role?: Maybe<UsersPermissionsRole>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type UsersPermissionsPermissionRelationResponseCollection = {
  __typename?: 'UsersPermissionsPermissionRelationResponseCollection';
  nodes: Array<UsersPermissionsPermission>;
};

export type UsersPermissionsRoleFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  description?: InputMaybe<StringFilterInput>;
  type?: InputMaybe<StringFilterInput>;
  permissions?: InputMaybe<UsersPermissionsPermissionFiltersInput>;
  users?: InputMaybe<UsersPermissionsUserFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UsersPermissionsRoleFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UsersPermissionsRoleFiltersInput>>>;
  not?: InputMaybe<UsersPermissionsRoleFiltersInput>;
};

export type UsersPermissionsRoleInput = {
  name?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['String']>;
  permissions?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  users?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type UsersPermissionsRole = {
  __typename?: 'UsersPermissionsRole';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  description?: Maybe<Scalars['String']>;
  type?: Maybe<Scalars['String']>;
  permissions_connection?: Maybe<UsersPermissionsPermissionRelationResponseCollection>;
  permissions: Array<Maybe<UsersPermissionsPermission>>;
  users_connection?: Maybe<UsersPermissionsUserRelationResponseCollection>;
  users: Array<Maybe<UsersPermissionsUser>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type UsersPermissionsRolePermissions_ConnectionArgs = {
  filters?: InputMaybe<UsersPermissionsPermissionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsRolePermissionsArgs = {
  filters?: InputMaybe<UsersPermissionsPermissionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsRoleUsers_ConnectionArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsRoleUsersArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type UsersPermissionsRoleEntityResponseCollection = {
  __typename?: 'UsersPermissionsRoleEntityResponseCollection';
  nodes: Array<UsersPermissionsRole>;
  pageInfo: Pagination;
};

export enum Enum_Userspermissionsuser_State {
  Novacek = 'Novacek',
  Aktivni = 'Aktivni',
  Neaktivni = 'Neaktivni',
  Alumni = 'Alumni',
  Neznamo = 'Neznamo'
}

export type UsersPermissionsUserFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  username?: InputMaybe<StringFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  provider?: InputMaybe<StringFilterInput>;
  confirmed?: InputMaybe<BooleanFilterInput>;
  blocked?: InputMaybe<BooleanFilterInput>;
  role?: InputMaybe<UsersPermissionsRoleFiltersInput>;
  firstname?: InputMaybe<StringFilterInput>;
  lastname?: InputMaybe<StringFilterInput>;
  nickname?: InputMaybe<StringFilterInput>;
  phoneNumber?: InputMaybe<StringFilterInput>;
  bankAccountNumber?: InputMaybe<StringFilterInput>;
  birthday?: InputMaybe<DateFilterInput>;
  address?: InputMaybe<StringFilterInput>;
  isMember?: InputMaybe<BooleanFilterInput>;
  state?: InputMaybe<StringFilterInput>;
  profilePictures?: InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>;
  bio?: InputMaybe<StringFilterInput>;
  webArticles?: InputMaybe<WebArticleFiltersInput>;
  tda_evaluation_teams?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  mailing_templates?: InputMaybe<MailingTemplateFiltersInput>;
  mailing_sources?: InputMaybe<MailingSourceFiltersInput>;
  mailing_batches?: InputMaybe<MailingBatchFiltersInput>;
  mailing_themes?: InputMaybe<MailingThemeFiltersInput>;
  discordId?: InputMaybe<LongFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<UsersPermissionsUserFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<UsersPermissionsUserFiltersInput>>>;
  not?: InputMaybe<UsersPermissionsUserFiltersInput>;
};

export type UsersPermissionsUserInput = {
  username?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  provider?: InputMaybe<Scalars['String']>;
  confirmed?: InputMaybe<Scalars['Boolean']>;
  blocked?: InputMaybe<Scalars['Boolean']>;
  role?: InputMaybe<Scalars['ID']>;
  firstname?: InputMaybe<Scalars['String']>;
  lastname?: InputMaybe<Scalars['String']>;
  nickname?: InputMaybe<Scalars['String']>;
  phoneNumber?: InputMaybe<Scalars['String']>;
  bankAccountNumber?: InputMaybe<Scalars['String']>;
  birthday?: InputMaybe<Scalars['Date']>;
  address?: InputMaybe<Scalars['String']>;
  isMember?: InputMaybe<Scalars['Boolean']>;
  state?: InputMaybe<Enum_Userspermissionsuser_State>;
  profilePictures?: InputMaybe<Array<InputMaybe<ComponentMultimediaBlocksCroppedImageInput>>>;
  bio?: InputMaybe<Scalars['String']>;
  webArticles?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  tda_evaluation_teams?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  mailing_templates?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  mailing_sources?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  mailing_batches?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  mailing_themes?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  discordId?: InputMaybe<Scalars['Long']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
  password?: InputMaybe<Scalars['String']>;
};

export type UsersPermissionsUser = {
  __typename?: 'UsersPermissionsUser';
  documentId: Scalars['ID'];
  username: Scalars['String'];
  email: Scalars['String'];
  provider?: Maybe<Scalars['String']>;
  confirmed?: Maybe<Scalars['Boolean']>;
  blocked?: Maybe<Scalars['Boolean']>;
  role?: Maybe<UsersPermissionsRole>;
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  nickname?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
  bankAccountNumber?: Maybe<Scalars['String']>;
  birthday?: Maybe<Scalars['Date']>;
  address?: Maybe<Scalars['String']>;
  isMember: Scalars['Boolean'];
  state: Enum_Userspermissionsuser_State;
  profilePictures?: Maybe<Array<Maybe<ComponentMultimediaBlocksCroppedImage>>>;
  bio?: Maybe<Scalars['String']>;
  webArticles_connection?: Maybe<WebArticleRelationResponseCollection>;
  webArticles: Array<Maybe<WebArticle>>;
  tda_evaluation_teams_connection?: Maybe<TdaSpechfishEvaluationTeamRelationResponseCollection>;
  tda_evaluation_teams: Array<Maybe<TdaSpechfishEvaluationTeam>>;
  mailing_templates_connection?: Maybe<MailingTemplateRelationResponseCollection>;
  mailing_templates: Array<Maybe<MailingTemplate>>;
  mailing_sources_connection?: Maybe<MailingSourceRelationResponseCollection>;
  mailing_sources: Array<Maybe<MailingSource>>;
  mailing_batches_connection?: Maybe<MailingBatchRelationResponseCollection>;
  mailing_batches: Array<Maybe<MailingBatch>>;
  mailing_themes_connection?: Maybe<MailingThemeRelationResponseCollection>;
  mailing_themes: Array<Maybe<MailingTheme>>;
  discordId?: Maybe<Scalars['Long']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type UsersPermissionsUserProfilePicturesArgs = {
  filters?: InputMaybe<ComponentMultimediaBlocksCroppedImageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserWebArticles_ConnectionArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserWebArticlesArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserTda_Evaluation_Teams_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserTda_Evaluation_TeamsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_Templates_ConnectionArgs = {
  filters?: InputMaybe<MailingTemplateFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_TemplatesArgs = {
  filters?: InputMaybe<MailingTemplateFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_Sources_ConnectionArgs = {
  filters?: InputMaybe<MailingSourceFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_SourcesArgs = {
  filters?: InputMaybe<MailingSourceFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_Batches_ConnectionArgs = {
  filters?: InputMaybe<MailingBatchFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_BatchesArgs = {
  filters?: InputMaybe<MailingBatchFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_Themes_ConnectionArgs = {
  filters?: InputMaybe<MailingThemeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type UsersPermissionsUserMailing_ThemesArgs = {
  filters?: InputMaybe<MailingThemeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type UsersPermissionsUserEntityResponse = {
  __typename?: 'UsersPermissionsUserEntityResponse';
  data?: Maybe<UsersPermissionsUser>;
};

export type UsersPermissionsUserEntityResponseCollection = {
  __typename?: 'UsersPermissionsUserEntityResponseCollection';
  nodes: Array<UsersPermissionsUser>;
  pageInfo: Pagination;
};

export type UsersPermissionsUserRelationResponseCollection = {
  __typename?: 'UsersPermissionsUserRelationResponseCollection';
  nodes: Array<UsersPermissionsUser>;
};

export enum Enum_Corecontestant_Role {
  TeamLeader = 'teamLeader',
  TeamMember = 'teamMember',
  TeamSubstitute = 'teamSubstitute'
}

export enum Enum_Corecontestant_Gender {
  Man = 'Man',
  Woman = 'Woman',
  Other = 'Other'
}

export type CoreContestantFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  firstname?: InputMaybe<StringFilterInput>;
  lastname?: InputMaybe<StringFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  phoneNumber?: InputMaybe<StringFilterInput>;
  school?: InputMaybe<CoreSchoolFiltersInput>;
  role?: InputMaybe<StringFilterInput>;
  tdaTeam?: InputMaybe<TdaCoreTeamFiltersInput>;
  gender?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreContestantFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreContestantFiltersInput>>>;
  not?: InputMaybe<CoreContestantFiltersInput>;
};

export type CoreContestantInput = {
  firstname?: InputMaybe<Scalars['String']>;
  lastname?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  phoneNumber?: InputMaybe<Scalars['String']>;
  school?: InputMaybe<Scalars['ID']>;
  role?: InputMaybe<Enum_Corecontestant_Role>;
  tdaTeam?: InputMaybe<Scalars['ID']>;
  gender?: InputMaybe<Enum_Corecontestant_Gender>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreContestant = {
  __typename?: 'CoreContestant';
  documentId: Scalars['ID'];
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  email: Scalars['String'];
  phoneNumber?: Maybe<Scalars['String']>;
  school?: Maybe<CoreSchool>;
  role: Enum_Corecontestant_Role;
  tdaTeam?: Maybe<TdaCoreTeam>;
  gender?: Maybe<Enum_Corecontestant_Gender>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type CoreContestantEntityResponseCollection = {
  __typename?: 'CoreContestantEntityResponseCollection';
  nodes: Array<CoreContestant>;
  pageInfo: Pagination;
};

export type CoreContestantRelationResponseCollection = {
  __typename?: 'CoreContestantRelationResponseCollection';
  nodes: Array<CoreContestant>;
};

export enum Enum_Coreevent_State {
  Neotevrena = 'Neotevrena',
  Otevrena = 'Otevrena',
  Uzavrena = 'Uzavrena',
  Zrusena = 'Zrusena'
}

export type CoreEventFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  eventGroup?: InputMaybe<CoreEventGroupFiltersInput>;
  state?: InputMaybe<StringFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  school?: InputMaybe<CoreSchoolFiltersInput>;
  place?: InputMaybe<StringFilterInput>;
  link?: InputMaybe<StringFilterInput>;
  startAt?: InputMaybe<DateTimeFilterInput>;
  capacity?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreEventFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreEventFiltersInput>>>;
  not?: InputMaybe<CoreEventFiltersInput>;
};

export type CoreEventInput = {
  eventGroup?: InputMaybe<Scalars['ID']>;
  state?: InputMaybe<Enum_Coreevent_State>;
  name?: InputMaybe<Scalars['String']>;
  school?: InputMaybe<Scalars['ID']>;
  place?: InputMaybe<Scalars['String']>;
  link?: InputMaybe<Scalars['String']>;
  startAt?: InputMaybe<Scalars['DateTime']>;
  capacity?: InputMaybe<Scalars['Int']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreEvent = {
  __typename?: 'CoreEvent';
  documentId: Scalars['ID'];
  eventGroup?: Maybe<CoreEventGroup>;
  state: Enum_Coreevent_State;
  name?: Maybe<Scalars['String']>;
  school?: Maybe<CoreSchool>;
  place?: Maybe<Scalars['String']>;
  link?: Maybe<Scalars['String']>;
  startAt: Scalars['DateTime'];
  capacity?: Maybe<Scalars['Int']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type CoreEventEntityResponseCollection = {
  __typename?: 'CoreEventEntityResponseCollection';
  nodes: Array<CoreEvent>;
  pageInfo: Pagination;
};

export type CoreEventRelationResponseCollection = {
  __typename?: 'CoreEventRelationResponseCollection';
  nodes: Array<CoreEvent>;
};

export type CoreEventGroupFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  project?: InputMaybe<CoreProjectFiltersInput>;
  name?: InputMaybe<StringFilterInput>;
  previousEventGroup?: InputMaybe<CoreEventGroupFiltersInput>;
  events?: InputMaybe<CoreEventFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreEventGroupFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreEventGroupFiltersInput>>>;
  not?: InputMaybe<CoreEventGroupFiltersInput>;
};

export type CoreEventGroupInput = {
  project?: InputMaybe<Scalars['ID']>;
  name?: InputMaybe<Scalars['String']>;
  previousEventGroup?: InputMaybe<Scalars['ID']>;
  events?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreEventGroup = {
  __typename?: 'CoreEventGroup';
  documentId: Scalars['ID'];
  project?: Maybe<CoreProject>;
  name: Scalars['String'];
  previousEventGroup?: Maybe<CoreEventGroup>;
  events_connection?: Maybe<CoreEventRelationResponseCollection>;
  events: Array<Maybe<CoreEvent>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type CoreEventGroupEvents_ConnectionArgs = {
  filters?: InputMaybe<CoreEventFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreEventGroupEventsArgs = {
  filters?: InputMaybe<CoreEventFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type CoreEventGroupEntityResponseCollection = {
  __typename?: 'CoreEventGroupEntityResponseCollection';
  nodes: Array<CoreEventGroup>;
  pageInfo: Pagination;
};

export type CoreEventGroupRelationResponseCollection = {
  __typename?: 'CoreEventGroupRelationResponseCollection';
  nodes: Array<CoreEventGroup>;
};

export type CoreProjectFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  projectType?: InputMaybe<CoreProjectTypeFiltersInput>;
  year?: InputMaybe<IntFilterInput>;
  customName?: InputMaybe<StringFilterInput>;
  isRunning?: InputMaybe<BooleanFilterInput>;
  eventGroups?: InputMaybe<CoreEventGroupFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreProjectFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreProjectFiltersInput>>>;
  not?: InputMaybe<CoreProjectFiltersInput>;
};

export type CoreProjectInput = {
  projectType?: InputMaybe<Scalars['ID']>;
  year?: InputMaybe<Scalars['Int']>;
  customName?: InputMaybe<Scalars['String']>;
  isRunning?: InputMaybe<Scalars['Boolean']>;
  eventGroups?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreProject = {
  __typename?: 'CoreProject';
  documentId: Scalars['ID'];
  projectType?: Maybe<CoreProjectType>;
  year: Scalars['Int'];
  customName?: Maybe<Scalars['String']>;
  isRunning: Scalars['Boolean'];
  eventGroups_connection?: Maybe<CoreEventGroupRelationResponseCollection>;
  eventGroups: Array<Maybe<CoreEventGroup>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type CoreProjectEventGroups_ConnectionArgs = {
  filters?: InputMaybe<CoreEventGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreProjectEventGroupsArgs = {
  filters?: InputMaybe<CoreEventGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type CoreProjectEntityResponseCollection = {
  __typename?: 'CoreProjectEntityResponseCollection';
  nodes: Array<CoreProject>;
  pageInfo: Pagination;
};

export type CoreProjectRelationResponseCollection = {
  __typename?: 'CoreProjectRelationResponseCollection';
  nodes: Array<CoreProject>;
};

export type CoreProjectTypeFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  tagColor?: InputMaybe<StringFilterInput>;
  projects?: InputMaybe<CoreProjectFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreProjectTypeFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreProjectTypeFiltersInput>>>;
  not?: InputMaybe<CoreProjectTypeFiltersInput>;
};

export type CoreProjectTypeInput = {
  name?: InputMaybe<Scalars['String']>;
  tagColor?: InputMaybe<Scalars['String']>;
  projects?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreProjectType = {
  __typename?: 'CoreProjectType';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  tagColor?: Maybe<Scalars['String']>;
  projects_connection?: Maybe<CoreProjectRelationResponseCollection>;
  projects: Array<Maybe<CoreProject>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type CoreProjectTypeProjects_ConnectionArgs = {
  filters?: InputMaybe<CoreProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreProjectTypeProjectsArgs = {
  filters?: InputMaybe<CoreProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type CoreProjectTypeEntityResponseCollection = {
  __typename?: 'CoreProjectTypeEntityResponseCollection';
  nodes: Array<CoreProjectType>;
  pageInfo: Pagination;
};

export type CoreRegionFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  childRegions?: InputMaybe<CoreRegionFiltersInput>;
  schools?: InputMaybe<CoreSchoolFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreRegionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreRegionFiltersInput>>>;
  not?: InputMaybe<CoreRegionFiltersInput>;
};

export type CoreRegionInput = {
  name?: InputMaybe<Scalars['String']>;
  childRegions?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  schools?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreRegion = {
  __typename?: 'CoreRegion';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  childRegions_connection?: Maybe<CoreRegionRelationResponseCollection>;
  childRegions: Array<Maybe<CoreRegion>>;
  schools_connection?: Maybe<CoreSchoolRelationResponseCollection>;
  schools: Array<Maybe<CoreSchool>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type CoreRegionChildRegions_ConnectionArgs = {
  filters?: InputMaybe<CoreRegionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreRegionChildRegionsArgs = {
  filters?: InputMaybe<CoreRegionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreRegionSchools_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreRegionSchoolsArgs = {
  filters?: InputMaybe<CoreSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type CoreRegionEntityResponseCollection = {
  __typename?: 'CoreRegionEntityResponseCollection';
  nodes: Array<CoreRegion>;
  pageInfo: Pagination;
};

export type CoreRegionRelationResponseCollection = {
  __typename?: 'CoreRegionRelationResponseCollection';
  nodes: Array<CoreRegion>;
};

export type CoreSchoolFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  contactPhone?: InputMaybe<StringFilterInput>;
  street?: InputMaybe<StringFilterInput>;
  town?: InputMaybe<StringFilterInput>;
  redizo?: InputMaybe<StringFilterInput>;
  web?: InputMaybe<StringFilterInput>;
  shortName?: InputMaybe<StringFilterInput>;
  schoolTypes?: InputMaybe<CoreSchoolTypeFiltersInput>;
  latitude?: InputMaybe<FloatFilterInput>;
  longitude?: InputMaybe<FloatFilterInput>;
  region?: InputMaybe<CoreRegionFiltersInput>;
  cin?: InputMaybe<StringFilterInput>;
  contactEmail?: InputMaybe<StringFilterInput>;
  schoolContacts?: InputMaybe<CoreSchoolContactFiltersInput>;
  contestants?: InputMaybe<CoreContestantFiltersInput>;
  postCode?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreSchoolFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreSchoolFiltersInput>>>;
  not?: InputMaybe<CoreSchoolFiltersInput>;
};

export type CoreSchoolInput = {
  name?: InputMaybe<Scalars['String']>;
  contactPhone?: InputMaybe<Scalars['String']>;
  street?: InputMaybe<Scalars['String']>;
  town?: InputMaybe<Scalars['String']>;
  redizo?: InputMaybe<Scalars['String']>;
  web?: InputMaybe<Scalars['String']>;
  shortName?: InputMaybe<Scalars['String']>;
  schoolTypes?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  latitude?: InputMaybe<Scalars['Float']>;
  longitude?: InputMaybe<Scalars['Float']>;
  region?: InputMaybe<Scalars['ID']>;
  cin?: InputMaybe<Scalars['String']>;
  contactEmail?: InputMaybe<Scalars['String']>;
  schoolContacts?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  contestants?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  postCode?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreSchool = {
  __typename?: 'CoreSchool';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  contactPhone?: Maybe<Scalars['String']>;
  street?: Maybe<Scalars['String']>;
  town?: Maybe<Scalars['String']>;
  redizo?: Maybe<Scalars['String']>;
  web?: Maybe<Scalars['String']>;
  shortName?: Maybe<Scalars['String']>;
  schoolTypes_connection?: Maybe<CoreSchoolTypeRelationResponseCollection>;
  schoolTypes: Array<Maybe<CoreSchoolType>>;
  latitude?: Maybe<Scalars['Float']>;
  longitude?: Maybe<Scalars['Float']>;
  region?: Maybe<CoreRegion>;
  cin?: Maybe<Scalars['String']>;
  contactEmail?: Maybe<Scalars['String']>;
  schoolContacts_connection?: Maybe<CoreSchoolContactRelationResponseCollection>;
  schoolContacts: Array<Maybe<CoreSchoolContact>>;
  contestants_connection?: Maybe<CoreContestantRelationResponseCollection>;
  contestants: Array<Maybe<CoreContestant>>;
  postCode?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type CoreSchoolSchoolTypes_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolSchoolTypesArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolSchoolContacts_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolSchoolContactsArgs = {
  filters?: InputMaybe<CoreSchoolContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolContestants_ConnectionArgs = {
  filters?: InputMaybe<CoreContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolContestantsArgs = {
  filters?: InputMaybe<CoreContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type CoreSchoolEntityResponseCollection = {
  __typename?: 'CoreSchoolEntityResponseCollection';
  nodes: Array<CoreSchool>;
  pageInfo: Pagination;
};

export type CoreSchoolRelationResponseCollection = {
  __typename?: 'CoreSchoolRelationResponseCollection';
  nodes: Array<CoreSchool>;
};

export enum Enum_Coreschoolcontact_Gender {
  Man = 'Man',
  Woman = 'Woman',
  Other = 'Other'
}

export type CoreSchoolContactFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  firstname?: InputMaybe<StringFilterInput>;
  lastname?: InputMaybe<StringFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  phoneNumber?: InputMaybe<StringFilterInput>;
  school?: InputMaybe<CoreSchoolFiltersInput>;
  gender?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreSchoolContactFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreSchoolContactFiltersInput>>>;
  not?: InputMaybe<CoreSchoolContactFiltersInput>;
};

export type CoreSchoolContactInput = {
  firstname?: InputMaybe<Scalars['String']>;
  lastname?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  phoneNumber?: InputMaybe<Scalars['String']>;
  school?: InputMaybe<Scalars['ID']>;
  gender?: InputMaybe<Enum_Coreschoolcontact_Gender>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreSchoolContact = {
  __typename?: 'CoreSchoolContact';
  documentId: Scalars['ID'];
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  email: Scalars['String'];
  phoneNumber?: Maybe<Scalars['String']>;
  school?: Maybe<CoreSchool>;
  gender?: Maybe<Enum_Coreschoolcontact_Gender>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type CoreSchoolContactEntityResponseCollection = {
  __typename?: 'CoreSchoolContactEntityResponseCollection';
  nodes: Array<CoreSchoolContact>;
  pageInfo: Pagination;
};

export type CoreSchoolContactRelationResponseCollection = {
  __typename?: 'CoreSchoolContactRelationResponseCollection';
  nodes: Array<CoreSchoolContact>;
};

export type CoreSchoolTypeFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  schools?: InputMaybe<CoreSchoolFiltersInput>;
  subtypes?: InputMaybe<CoreSchoolTypeFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<CoreSchoolTypeFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<CoreSchoolTypeFiltersInput>>>;
  not?: InputMaybe<CoreSchoolTypeFiltersInput>;
};

export type CoreSchoolTypeInput = {
  name?: InputMaybe<Scalars['String']>;
  schools?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  subtypes?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CoreSchoolType = {
  __typename?: 'CoreSchoolType';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  schools_connection?: Maybe<CoreSchoolRelationResponseCollection>;
  schools: Array<Maybe<CoreSchool>>;
  subtypes_connection?: Maybe<CoreSchoolTypeRelationResponseCollection>;
  subtypes: Array<Maybe<CoreSchoolType>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type CoreSchoolTypeSchools_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolTypeSchoolsArgs = {
  filters?: InputMaybe<CoreSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolTypeSubtypes_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type CoreSchoolTypeSubtypesArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type CoreSchoolTypeEntityResponseCollection = {
  __typename?: 'CoreSchoolTypeEntityResponseCollection';
  nodes: Array<CoreSchoolType>;
  pageInfo: Pagination;
};

export type CoreSchoolTypeRelationResponseCollection = {
  __typename?: 'CoreSchoolTypeRelationResponseCollection';
  nodes: Array<CoreSchoolType>;
};

export type DummyFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  text?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<DummyFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<DummyFiltersInput>>>;
  not?: InputMaybe<DummyFiltersInput>;
};

export type DummyInput = {
  text?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type Dummy = {
  __typename?: 'Dummy';
  documentId: Scalars['ID'];
  text?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type DummyEntityResponseCollection = {
  __typename?: 'DummyEntityResponseCollection';
  nodes: Array<Dummy>;
  pageInfo: Pagination;
};

export enum Enum_Mailingbatch_Status {
  Completed = 'completed',
  Failed = 'failed',
  Wip = 'wip'
}

export type MailingBatchFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  author?: InputMaybe<UsersPermissionsUserFiltersInput>;
  mails?: InputMaybe<ComponentMailingListOfSentEMailsFiltersInput>;
  status?: InputMaybe<StringFilterInput>;
  template?: InputMaybe<MailingTemplateFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<MailingBatchFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<MailingBatchFiltersInput>>>;
  not?: InputMaybe<MailingBatchFiltersInput>;
};

export type MailingBatchInput = {
  author?: InputMaybe<Scalars['ID']>;
  mails?: InputMaybe<Array<InputMaybe<ComponentMailingListOfSentEMailsInput>>>;
  status?: InputMaybe<Enum_Mailingbatch_Status>;
  template?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type MailingBatch = {
  __typename?: 'MailingBatch';
  documentId: Scalars['ID'];
  author?: Maybe<UsersPermissionsUser>;
  mails: Array<Maybe<ComponentMailingListOfSentEMails>>;
  status: Enum_Mailingbatch_Status;
  template?: Maybe<MailingTemplate>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type MailingBatchMailsArgs = {
  filters?: InputMaybe<ComponentMailingListOfSentEMailsFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type MailingBatchEntityResponseCollection = {
  __typename?: 'MailingBatchEntityResponseCollection';
  nodes: Array<MailingBatch>;
  pageInfo: Pagination;
};

export type MailingBatchRelationResponseCollection = {
  __typename?: 'MailingBatchRelationResponseCollection';
  nodes: Array<MailingBatch>;
};

export type MailingBlacklistInput = {
  mails?: InputMaybe<Array<InputMaybe<ComponentMailingFullEMailInput>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type MailingBlacklist = {
  __typename?: 'MailingBlacklist';
  documentId: Scalars['ID'];
  mails?: Maybe<Array<Maybe<ComponentMailingFullEMail>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type MailingBlacklistMailsArgs = {
  filters?: InputMaybe<ComponentMailingFullEMailFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type MailingSourceFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  author?: InputMaybe<UsersPermissionsUserFiltersInput>;
  mails?: InputMaybe<ComponentMailingFullEMailFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<MailingSourceFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<MailingSourceFiltersInput>>>;
  not?: InputMaybe<MailingSourceFiltersInput>;
};

export type MailingSourceInput = {
  name?: InputMaybe<Scalars['String']>;
  author?: InputMaybe<Scalars['ID']>;
  mails?: InputMaybe<Array<InputMaybe<ComponentMailingFullEMailInput>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type MailingSource = {
  __typename?: 'MailingSource';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  author?: Maybe<UsersPermissionsUser>;
  mails: Array<Maybe<ComponentMailingFullEMail>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type MailingSourceMailsArgs = {
  filters?: InputMaybe<ComponentMailingFullEMailFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type MailingSourceEntityResponseCollection = {
  __typename?: 'MailingSourceEntityResponseCollection';
  nodes: Array<MailingSource>;
  pageInfo: Pagination;
};

export type MailingSourceRelationResponseCollection = {
  __typename?: 'MailingSourceRelationResponseCollection';
  nodes: Array<MailingSource>;
};

export type MailingTemplateFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  author?: InputMaybe<UsersPermissionsUserFiltersInput>;
  subject?: InputMaybe<StringFilterInput>;
  theme?: InputMaybe<MailingThemeFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<MailingTemplateFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<MailingTemplateFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<MailingTemplateFiltersInput>>>;
  not?: InputMaybe<MailingTemplateFiltersInput>;
};

export type MailingTemplateInput = {
  name?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Scalars['String']>;
  author?: InputMaybe<Scalars['ID']>;
  subject?: InputMaybe<Scalars['String']>;
  theme?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type MailingTemplate = {
  __typename?: 'MailingTemplate';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  content: Scalars['String'];
  author?: Maybe<UsersPermissionsUser>;
  subject: Scalars['String'];
  theme?: Maybe<MailingTheme>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<MailingTemplateRelationResponseCollection>;
  localizations: Array<Maybe<MailingTemplate>>;
};


export type MailingTemplateLocalizations_ConnectionArgs = {
  filters?: InputMaybe<MailingTemplateFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type MailingTemplateLocalizationsArgs = {
  filters?: InputMaybe<MailingTemplateFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type MailingTemplateEntityResponseCollection = {
  __typename?: 'MailingTemplateEntityResponseCollection';
  nodes: Array<MailingTemplate>;
  pageInfo: Pagination;
};

export type MailingTemplateRelationResponseCollection = {
  __typename?: 'MailingTemplateRelationResponseCollection';
  nodes: Array<MailingTemplate>;
};

export enum Enum_Mailingtheme_Type {
  Html = 'html',
  Mjml = 'mjml'
}

export type MailingThemeFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  author?: InputMaybe<UsersPermissionsUserFiltersInput>;
  type?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<MailingThemeFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<MailingThemeFiltersInput>>>;
  not?: InputMaybe<MailingThemeFiltersInput>;
};

export type MailingThemeInput = {
  name?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Scalars['String']>;
  author?: InputMaybe<Scalars['ID']>;
  type?: InputMaybe<Enum_Mailingtheme_Type>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type MailingTheme = {
  __typename?: 'MailingTheme';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  content?: Maybe<Scalars['String']>;
  author?: Maybe<UsersPermissionsUser>;
  type: Enum_Mailingtheme_Type;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type MailingThemeEntityResponseCollection = {
  __typename?: 'MailingThemeEntityResponseCollection';
  nodes: Array<MailingTheme>;
  pageInfo: Pagination;
};

export type MailingThemeRelationResponseCollection = {
  __typename?: 'MailingThemeRelationResponseCollection';
  nodes: Array<MailingTheme>;
};

export type ScgBotConfigInput = {
  moderatorRoleId?: InputMaybe<Scalars['Long']>;
  vvRoleId?: InputMaybe<Scalars['Long']>;
  developers?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type ScgBotConfig = {
  __typename?: 'ScgBotConfig';
  documentId: Scalars['ID'];
  moderatorRoleId?: Maybe<Scalars['Long']>;
  vvRoleId?: Maybe<Scalars['Long']>;
  developers_connection?: Maybe<UsersPermissionsUserRelationResponseCollection>;
  developers: Array<Maybe<UsersPermissionsUser>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type ScgBotConfigDevelopers_ConnectionArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type ScgBotConfigDevelopersArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type ScgBotSuggestionFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  suggestion?: InputMaybe<StringFilterInput>;
  additional?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<ScgBotSuggestionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<ScgBotSuggestionFiltersInput>>>;
  not?: InputMaybe<ScgBotSuggestionFiltersInput>;
};

export type ScgBotSuggestionInput = {
  suggestion?: InputMaybe<Scalars['String']>;
  additional?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type ScgBotSuggestion = {
  __typename?: 'ScgBotSuggestion';
  documentId: Scalars['ID'];
  suggestion?: Maybe<Scalars['String']>;
  additional?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type ScgBotSuggestionEntityResponseCollection = {
  __typename?: 'ScgBotSuggestionEntityResponseCollection';
  nodes: Array<ScgBotSuggestion>;
  pageInfo: Pagination;
};

export type TdaBotInput = {
  adminRoleId?: InputMaybe<Scalars['String']>;
  modRoleId?: InputMaybe<Scalars['String']>;
  modLogChannelId?: InputMaybe<Scalars['String']>;
  guildId?: InputMaybe<Scalars['String']>;
  tagBannedUsers?: InputMaybe<Array<InputMaybe<ComponentDiscordDiscordIdListInput>>>;
  statusText?: InputMaybe<Scalars['String']>;
  statusType?: InputMaybe<Scalars['Int']>;
  orgRoleId?: InputMaybe<Scalars['String']>;
  noResponseMinHours?: InputMaybe<Scalars['Int']>;
  techSupportRoleId?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBot = {
  __typename?: 'TdaBot';
  documentId: Scalars['ID'];
  adminRoleId: Scalars['String'];
  modRoleId: Scalars['String'];
  modLogChannelId: Scalars['String'];
  guildId: Scalars['String'];
  tagBannedUsers?: Maybe<Array<Maybe<ComponentDiscordDiscordIdList>>>;
  statusText: Scalars['String'];
  statusType: Scalars['Int'];
  orgRoleId: Scalars['String'];
  noResponseMinHours: Scalars['Int'];
  techSupportRoleId: Scalars['String'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaBotTagBannedUsersArgs = {
  filters?: InputMaybe<ComponentDiscordDiscordIdListFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaBotContestantFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  userId?: InputMaybe<StringFilterInput>;
  team?: InputMaybe<TdaCoreTeamFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaBotContestantFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaBotContestantFiltersInput>>>;
  not?: InputMaybe<TdaBotContestantFiltersInput>;
};

export type TdaBotContestantInput = {
  userId?: InputMaybe<Scalars['String']>;
  team?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBotContestant = {
  __typename?: 'TdaBotContestant';
  documentId: Scalars['ID'];
  userId: Scalars['String'];
  team?: Maybe<TdaCoreTeam>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaBotContestantEntityResponseCollection = {
  __typename?: 'TdaBotContestantEntityResponseCollection';
  nodes: Array<TdaBotContestant>;
  pageInfo: Pagination;
};

export type TdaBotTagFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  modOnly?: InputMaybe<BooleanFilterInput>;
  keyphrase?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaBotTagFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaBotTagFiltersInput>>>;
  not?: InputMaybe<TdaBotTagFiltersInput>;
};

export type TdaBotTagInput = {
  name?: InputMaybe<Scalars['String']>;
  content?: InputMaybe<Scalars['String']>;
  modOnly?: InputMaybe<Scalars['Boolean']>;
  keyphrase?: InputMaybe<Scalars['String']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBotTag = {
  __typename?: 'TdaBotTag';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  content?: Maybe<Scalars['String']>;
  modOnly: Scalars['Boolean'];
  keyphrase?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaBotTagEntityResponseCollection = {
  __typename?: 'TdaBotTagEntityResponseCollection';
  nodes: Array<TdaBotTag>;
  pageInfo: Pagination;
};

export type TdaBusherTeamTestFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  team?: InputMaybe<TdaCoreTeamFiltersInput>;
  test?: InputMaybe<TdaBusherTestFiltersInput>;
  result?: InputMaybe<FloatFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaBusherTeamTestFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaBusherTeamTestFiltersInput>>>;
  not?: InputMaybe<TdaBusherTeamTestFiltersInput>;
};

export type TdaBusherTeamTestInput = {
  team?: InputMaybe<Scalars['ID']>;
  test?: InputMaybe<Scalars['ID']>;
  result?: InputMaybe<Scalars['Float']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBusherTeamTest = {
  __typename?: 'TdaBusherTeamTest';
  documentId: Scalars['ID'];
  team?: Maybe<TdaCoreTeam>;
  test?: Maybe<TdaBusherTest>;
  result: Scalars['Float'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaBusherTeamTestEntityResponseCollection = {
  __typename?: 'TdaBusherTeamTestEntityResponseCollection';
  nodes: Array<TdaBusherTeamTest>;
  pageInfo: Pagination;
};

export enum Enum_Tdabushertest_Type {
  BunTest = 'bunTest',
  PyTest = 'pyTest'
}

export type TdaBusherTestFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  description?: InputMaybe<StringFilterInput>;
  startsAt?: InputMaybe<DateTimeFilterInput>;
  endsAt?: InputMaybe<DateTimeFilterInput>;
  filename?: InputMaybe<StringFilterInput>;
  type?: InputMaybe<StringFilterInput>;
  maxPoints?: InputMaybe<IntFilterInput>;
  shortDescription?: InputMaybe<StringFilterInput>;
  tda_phase?: InputMaybe<TdaCorePhaseFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaBusherTestFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaBusherTestFiltersInput>>>;
  not?: InputMaybe<TdaBusherTestFiltersInput>;
};

export type TdaBusherTestInput = {
  name?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  startsAt?: InputMaybe<Scalars['DateTime']>;
  endsAt?: InputMaybe<Scalars['DateTime']>;
  filename?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Enum_Tdabushertest_Type>;
  maxPoints?: InputMaybe<Scalars['Int']>;
  shortDescription?: InputMaybe<Scalars['String']>;
  tda_phase?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaBusherTest = {
  __typename?: 'TdaBusherTest';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  description?: Maybe<Scalars['String']>;
  startsAt: Scalars['DateTime'];
  endsAt: Scalars['DateTime'];
  filename: Scalars['String'];
  type: Enum_Tdabushertest_Type;
  maxPoints?: Maybe<Scalars['Int']>;
  shortDescription?: Maybe<Scalars['String']>;
  tda_phase?: Maybe<TdaCorePhase>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaBusherTestEntityResponseCollection = {
  __typename?: 'TdaBusherTestEntityResponseCollection';
  nodes: Array<TdaBusherTest>;
  pageInfo: Pagination;
};

export type TdaBusherTestRelationResponseCollection = {
  __typename?: 'TdaBusherTestRelationResponseCollection';
  nodes: Array<TdaBusherTest>;
};

export enum Enum_Tdacorephase_State {
  Undefined = 'undefined',
  PreNk = 'PRE_NK',
  Nk = 'NK',
  PostNk = 'POST_NK',
  PreSk = 'PRE_SK',
  Sk = 'SK',
  PostSk = 'POST_SK',
  PreGrf = 'PRE_GRF',
  Grf = 'GRF',
  PostGrf = 'POST_GRF'
}

export type TdaCorePhaseFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  startsAt?: InputMaybe<DateTimeFilterInput>;
  endsAt?: InputMaybe<DateTimeFilterInput>;
  state?: InputMaybe<StringFilterInput>;
  infoPanels?: InputMaybe<ComponentTdaFisherPhasesInfoFiltersInput>;
  tda_tests?: InputMaybe<TdaBusherTestFiltersInput>;
  tda_evaluation_sets?: InputMaybe<TdaSpechfishEvaluationSetFiltersInput>;
  project?: InputMaybe<CoreProjectFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaCorePhaseFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaCorePhaseFiltersInput>>>;
  not?: InputMaybe<TdaCorePhaseFiltersInput>;
};

export type TdaCorePhaseInput = {
  startsAt?: InputMaybe<Scalars['DateTime']>;
  endsAt?: InputMaybe<Scalars['DateTime']>;
  state?: InputMaybe<Enum_Tdacorephase_State>;
  infoPanels?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesInfoInput>>>;
  tda_tests?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  tda_evaluation_sets?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  project?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaCorePhase = {
  __typename?: 'TdaCorePhase';
  documentId: Scalars['ID'];
  startsAt: Scalars['DateTime'];
  endsAt: Scalars['DateTime'];
  state: Enum_Tdacorephase_State;
  infoPanels?: Maybe<Array<Maybe<ComponentTdaFisherPhasesInfo>>>;
  tda_tests_connection?: Maybe<TdaBusherTestRelationResponseCollection>;
  tda_tests: Array<Maybe<TdaBusherTest>>;
  tda_evaluation_sets_connection?: Maybe<TdaSpechfishEvaluationSetRelationResponseCollection>;
  tda_evaluation_sets: Array<Maybe<TdaSpechfishEvaluationSet>>;
  project?: Maybe<CoreProject>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaCorePhaseInfoPanelsArgs = {
  filters?: InputMaybe<ComponentTdaFisherPhasesInfoFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCorePhaseTda_Tests_ConnectionArgs = {
  filters?: InputMaybe<TdaBusherTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCorePhaseTda_TestsArgs = {
  filters?: InputMaybe<TdaBusherTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCorePhaseTda_Evaluation_Sets_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationSetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCorePhaseTda_Evaluation_SetsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationSetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaCorePhaseEntityResponseCollection = {
  __typename?: 'TdaCorePhaseEntityResponseCollection';
  nodes: Array<TdaCorePhase>;
  pageInfo: Pagination;
};

export enum Enum_Tdacoreteam_Level {
  Nk = 'NK',
  Sk = 'SK',
  Grf = 'GRF'
}

export type TdaCoreTeamFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  access?: InputMaybe<BooleanFilterInput>;
  uuid?: InputMaybe<StringFilterInput>;
  ghostID?: InputMaybe<IntFilterInput>;
  subdomain?: InputMaybe<StringFilterInput>;
  ownReferral?: InputMaybe<StringFilterInput>;
  usedReferral?: InputMaybe<StringFilterInput>;
  schoolContact?: InputMaybe<CoreSchoolContactFiltersInput>;
  contestants?: InputMaybe<CoreContestantFiltersInput>;
  allowedPartnerDataAccess?: InputMaybe<BooleanFilterInput>;
  discordCode?: InputMaybe<StringFilterInput>;
  tda_evaluation_teams?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  meets?: InputMaybe<TdaRezfishMeetFiltersInput>;
  level?: InputMaybe<StringFilterInput>;
  tda_tdc_application?: InputMaybe<TdaTdcApplicationFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaCoreTeamFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaCoreTeamFiltersInput>>>;
  not?: InputMaybe<TdaCoreTeamFiltersInput>;
};

export type TdaCoreTeamInput = {
  name?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
  access?: InputMaybe<Scalars['Boolean']>;
  uuid?: InputMaybe<Scalars['String']>;
  ghostID?: InputMaybe<Scalars['Int']>;
  subdomain?: InputMaybe<Scalars['String']>;
  ownReferral?: InputMaybe<Scalars['String']>;
  usedReferral?: InputMaybe<Scalars['String']>;
  schoolContact?: InputMaybe<Scalars['ID']>;
  contestants?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  allowedPartnerDataAccess?: InputMaybe<Scalars['Boolean']>;
  discordCode?: InputMaybe<Scalars['String']>;
  tda_evaluation_teams?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  meets?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  level?: InputMaybe<Enum_Tdacoreteam_Level>;
  tda_tdc_application?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaCoreTeam = {
  __typename?: 'TdaCoreTeam';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  url?: Maybe<Scalars['String']>;
  access: Scalars['Boolean'];
  uuid: Scalars['String'];
  ghostID?: Maybe<Scalars['Int']>;
  subdomain?: Maybe<Scalars['String']>;
  ownReferral?: Maybe<Scalars['String']>;
  usedReferral?: Maybe<Scalars['String']>;
  schoolContact?: Maybe<CoreSchoolContact>;
  contestants_connection?: Maybe<CoreContestantRelationResponseCollection>;
  contestants: Array<Maybe<CoreContestant>>;
  allowedPartnerDataAccess: Scalars['Boolean'];
  discordCode: Scalars['String'];
  tda_evaluation_teams_connection?: Maybe<TdaSpechfishEvaluationTeamRelationResponseCollection>;
  tda_evaluation_teams: Array<Maybe<TdaSpechfishEvaluationTeam>>;
  meets_connection?: Maybe<TdaRezfishMeetRelationResponseCollection>;
  meets: Array<Maybe<TdaRezfishMeet>>;
  level: Enum_Tdacoreteam_Level;
  tda_tdc_application?: Maybe<TdaTdcApplication>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaCoreTeamContestants_ConnectionArgs = {
  filters?: InputMaybe<CoreContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCoreTeamContestantsArgs = {
  filters?: InputMaybe<CoreContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCoreTeamTda_Evaluation_Teams_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCoreTeamTda_Evaluation_TeamsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCoreTeamMeets_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaCoreTeamMeetsArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaCoreTeamEntityResponseCollection = {
  __typename?: 'TdaCoreTeamEntityResponseCollection';
  nodes: Array<TdaCoreTeam>;
  pageInfo: Pagination;
};

export enum Enum_Tdafisherflag_Stage {
  Nk = 'NK',
  Sk = 'SK',
  Grf = 'GRF'
}

export enum Enum_Tdafisherflag_Typeofnote {
  Generic = 'generic',
  Email = 'email',
  StickyNote = 'stickyNote',
  Notepad = 'notepad'
}

export type TdaFisherFlagNoteDynamicZone = ComponentTdaCtfComponentsEmail | ComponentTdaCtfComponentsGenericNote | ComponentTdaCtfComponentsStickyNote | Error;

export type TdaFisherFlagFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  flag?: InputMaybe<StringFilterInput>;
  stage?: InputMaybe<StringFilterInput>;
  basePoints?: InputMaybe<IntFilterInput>;
  typeOfNote?: InputMaybe<StringFilterInput>;
  help?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaFisherFlagFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaFisherFlagFiltersInput>>>;
  not?: InputMaybe<TdaFisherFlagFiltersInput>;
};

export type TdaFisherFlagInput = {
  name?: InputMaybe<Scalars['String']>;
  flag?: InputMaybe<Scalars['String']>;
  stage?: InputMaybe<Enum_Tdafisherflag_Stage>;
  basePoints?: InputMaybe<Scalars['Int']>;
  typeOfNote?: InputMaybe<Enum_Tdafisherflag_Typeofnote>;
  help?: InputMaybe<Scalars['String']>;
  note?: InputMaybe<Array<Scalars['TdaFisherFlagNoteDynamicZoneInput']>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaFisherFlag = {
  __typename?: 'TdaFisherFlag';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  flag: Scalars['String'];
  stage: Enum_Tdafisherflag_Stage;
  basePoints: Scalars['Int'];
  typeOfNote?: Maybe<Enum_Tdafisherflag_Typeofnote>;
  help?: Maybe<Scalars['String']>;
  note?: Maybe<Array<Maybe<TdaFisherFlagNoteDynamicZone>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaFisherFlagEntityResponseCollection = {
  __typename?: 'TdaFisherFlagEntityResponseCollection';
  nodes: Array<TdaFisherFlag>;
  pageInfo: Pagination;
};

export type TdaFisherTeamFlagFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  team?: InputMaybe<TdaCoreTeamFiltersInput>;
  flag?: InputMaybe<TdaFisherFlagFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaFisherTeamFlagFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaFisherTeamFlagFiltersInput>>>;
  not?: InputMaybe<TdaFisherTeamFlagFiltersInput>;
};

export type TdaFisherTeamFlagInput = {
  team?: InputMaybe<Scalars['ID']>;
  flag?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaFisherTeamFlag = {
  __typename?: 'TdaFisherTeamFlag';
  documentId: Scalars['ID'];
  team?: Maybe<TdaCoreTeam>;
  flag?: Maybe<TdaFisherFlag>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaFisherTeamFlagEntityResponseCollection = {
  __typename?: 'TdaFisherTeamFlagEntityResponseCollection';
  nodes: Array<TdaFisherTeamFlag>;
  pageInfo: Pagination;
};

export type TdaFisherTeamResultFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  tda_phase?: InputMaybe<TdaCorePhaseFiltersInput>;
  publishAt?: InputMaybe<DateTimeFilterInput>;
  unpublishAt?: InputMaybe<DateTimeFilterInput>;
  files?: InputMaybe<ComponentTdaFisherPhasesResultFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaFisherTeamResultFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaFisherTeamResultFiltersInput>>>;
  not?: InputMaybe<TdaFisherTeamResultFiltersInput>;
};

export type TdaFisherTeamResultInput = {
  tda_phase?: InputMaybe<Scalars['ID']>;
  publishAt?: InputMaybe<Scalars['DateTime']>;
  unpublishAt?: InputMaybe<Scalars['DateTime']>;
  files?: InputMaybe<Array<InputMaybe<ComponentTdaFisherPhasesResultInput>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaFisherTeamResult = {
  __typename?: 'TdaFisherTeamResult';
  documentId: Scalars['ID'];
  tda_phase?: Maybe<TdaCorePhase>;
  publishAt: Scalars['DateTime'];
  unpublishAt?: Maybe<Scalars['DateTime']>;
  files?: Maybe<Array<Maybe<ComponentTdaFisherPhasesResult>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaFisherTeamResultFilesArgs = {
  filters?: InputMaybe<ComponentTdaFisherPhasesResultFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaFisherTeamResultEntityResponseCollection = {
  __typename?: 'TdaFisherTeamResultEntityResponseCollection';
  nodes: Array<TdaFisherTeamResult>;
  pageInfo: Pagination;
};

export type TdaRezfishMeetFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  date?: InputMaybe<DateFilterInput>;
  startTime?: InputMaybe<TimeFilterInput>;
  endTime?: InputMaybe<TimeFilterInput>;
  tda_team?: InputMaybe<TdaCoreTeamFiltersInput>;
  meet_length?: InputMaybe<TdaRezfishMeetLengthFiltersInput>;
  mentor?: InputMaybe<TdaRezfishMentorFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaRezfishMeetFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaRezfishMeetFiltersInput>>>;
  not?: InputMaybe<TdaRezfishMeetFiltersInput>;
};

export type TdaRezfishMeetInput = {
  date?: InputMaybe<Scalars['Date']>;
  startTime?: InputMaybe<Scalars['Time']>;
  endTime?: InputMaybe<Scalars['Time']>;
  tda_team?: InputMaybe<Scalars['ID']>;
  meet_length?: InputMaybe<Scalars['ID']>;
  mentor?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaRezfishMeet = {
  __typename?: 'TdaRezfishMeet';
  documentId: Scalars['ID'];
  date?: Maybe<Scalars['Date']>;
  startTime?: Maybe<Scalars['Time']>;
  endTime?: Maybe<Scalars['Time']>;
  tda_team?: Maybe<TdaCoreTeam>;
  meet_length?: Maybe<TdaRezfishMeetLength>;
  mentor?: Maybe<TdaRezfishMentor>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaRezfishMeetEntityResponseCollection = {
  __typename?: 'TdaRezfishMeetEntityResponseCollection';
  nodes: Array<TdaRezfishMeet>;
  pageInfo: Pagination;
};

export type TdaRezfishMeetRelationResponseCollection = {
  __typename?: 'TdaRezfishMeetRelationResponseCollection';
  nodes: Array<TdaRezfishMeet>;
};

export type TdaRezfishMeetCategoryFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  minMeets?: InputMaybe<IntFilterInput>;
  maxMeets?: InputMaybe<IntFilterInput>;
  mentors?: InputMaybe<TdaRezfishMentorFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaRezfishMeetCategoryFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaRezfishMeetCategoryFiltersInput>>>;
  not?: InputMaybe<TdaRezfishMeetCategoryFiltersInput>;
};

export type TdaRezfishMeetCategoryInput = {
  name?: InputMaybe<Scalars['String']>;
  minMeets?: InputMaybe<Scalars['Int']>;
  maxMeets?: InputMaybe<Scalars['Int']>;
  mentors?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaRezfishMeetCategory = {
  __typename?: 'TdaRezfishMeetCategory';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  minMeets?: Maybe<Scalars['Int']>;
  maxMeets?: Maybe<Scalars['Int']>;
  mentors_connection?: Maybe<TdaRezfishMentorRelationResponseCollection>;
  mentors: Array<Maybe<TdaRezfishMentor>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaRezfishMeetCategoryMentors_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMeetCategoryMentorsArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaRezfishMeetCategoryEntityResponseCollection = {
  __typename?: 'TdaRezfishMeetCategoryEntityResponseCollection';
  nodes: Array<TdaRezfishMeetCategory>;
  pageInfo: Pagination;
};

export type TdaRezfishMeetCategoryRelationResponseCollection = {
  __typename?: 'TdaRezfishMeetCategoryRelationResponseCollection';
  nodes: Array<TdaRezfishMeetCategory>;
};

export type TdaRezfishMeetLengthFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  length?: InputMaybe<IntFilterInput>;
  mentor?: InputMaybe<TdaRezfishMentorFiltersInput>;
  meets?: InputMaybe<TdaRezfishMeetFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaRezfishMeetLengthFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaRezfishMeetLengthFiltersInput>>>;
  not?: InputMaybe<TdaRezfishMeetLengthFiltersInput>;
};

export type TdaRezfishMeetLengthInput = {
  length?: InputMaybe<Scalars['Int']>;
  mentor?: InputMaybe<Scalars['ID']>;
  meets?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaRezfishMeetLength = {
  __typename?: 'TdaRezfishMeetLength';
  documentId: Scalars['ID'];
  length?: Maybe<Scalars['Int']>;
  mentor?: Maybe<TdaRezfishMentor>;
  meets_connection?: Maybe<TdaRezfishMeetRelationResponseCollection>;
  meets: Array<Maybe<TdaRezfishMeet>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaRezfishMeetLengthMeets_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMeetLengthMeetsArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaRezfishMeetLengthEntityResponseCollection = {
  __typename?: 'TdaRezfishMeetLengthEntityResponseCollection';
  nodes: Array<TdaRezfishMeetLength>;
  pageInfo: Pagination;
};

export type TdaRezfishMeetLengthRelationResponseCollection = {
  __typename?: 'TdaRezfishMeetLengthRelationResponseCollection';
  nodes: Array<TdaRezfishMeetLength>;
};

export type TdaRezfishMentorFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  firstname?: InputMaybe<StringFilterInput>;
  lastname?: InputMaybe<StringFilterInput>;
  bio?: InputMaybe<StringFilterInput>;
  email?: InputMaybe<StringFilterInput>;
  availableTimes?: InputMaybe<ComponentTdaRezfishMeetComponentsAvailableTimeFiltersInput>;
  token?: InputMaybe<StringFilterInput>;
  created?: InputMaybe<BooleanFilterInput>;
  tags?: InputMaybe<TdaRezfishTagFiltersInput>;
  platform?: InputMaybe<TdaRezfishPlatformFiltersInput>;
  meets?: InputMaybe<TdaRezfishMeetFiltersInput>;
  meet_categories?: InputMaybe<TdaRezfishMeetCategoryFiltersInput>;
  meet_lengths?: InputMaybe<TdaRezfishMeetLengthFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaRezfishMentorFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaRezfishMentorFiltersInput>>>;
  not?: InputMaybe<TdaRezfishMentorFiltersInput>;
};

export type TdaRezfishMentorInput = {
  firstname?: InputMaybe<Scalars['String']>;
  lastname?: InputMaybe<Scalars['String']>;
  bio?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  availableTimes?: InputMaybe<ComponentTdaRezfishMeetComponentsAvailableTimeInput>;
  token?: InputMaybe<Scalars['String']>;
  created?: InputMaybe<Scalars['Boolean']>;
  profilePic?: InputMaybe<Scalars['ID']>;
  tags?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  platform?: InputMaybe<Scalars['ID']>;
  meets?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  meet_categories?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  meet_lengths?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaRezfishMentor = {
  __typename?: 'TdaRezfishMentor';
  documentId: Scalars['ID'];
  firstname?: Maybe<Scalars['String']>;
  lastname?: Maybe<Scalars['String']>;
  bio?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  availableTimes?: Maybe<ComponentTdaRezfishMeetComponentsAvailableTime>;
  token?: Maybe<Scalars['String']>;
  created?: Maybe<Scalars['Boolean']>;
  profilePic?: Maybe<UploadFile>;
  tags_connection?: Maybe<TdaRezfishTagRelationResponseCollection>;
  tags: Array<Maybe<TdaRezfishTag>>;
  platform?: Maybe<TdaRezfishPlatform>;
  meets_connection?: Maybe<TdaRezfishMeetRelationResponseCollection>;
  meets: Array<Maybe<TdaRezfishMeet>>;
  meet_categories_connection?: Maybe<TdaRezfishMeetCategoryRelationResponseCollection>;
  meet_categories: Array<Maybe<TdaRezfishMeetCategory>>;
  meet_lengths_connection?: Maybe<TdaRezfishMeetLengthRelationResponseCollection>;
  meet_lengths: Array<Maybe<TdaRezfishMeetLength>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaRezfishMentorTags_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishTagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMentorTagsArgs = {
  filters?: InputMaybe<TdaRezfishTagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMentorMeets_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMentorMeetsArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMentorMeet_Categories_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMentorMeet_CategoriesArgs = {
  filters?: InputMaybe<TdaRezfishMeetCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMentorMeet_Lengths_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetLengthFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishMentorMeet_LengthsArgs = {
  filters?: InputMaybe<TdaRezfishMeetLengthFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaRezfishMentorEntityResponseCollection = {
  __typename?: 'TdaRezfishMentorEntityResponseCollection';
  nodes: Array<TdaRezfishMentor>;
  pageInfo: Pagination;
};

export type TdaRezfishMentorRelationResponseCollection = {
  __typename?: 'TdaRezfishMentorRelationResponseCollection';
  nodes: Array<TdaRezfishMentor>;
};

export type TdaRezfishPlatformFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  mentors?: InputMaybe<TdaRezfishMentorFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaRezfishPlatformFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaRezfishPlatformFiltersInput>>>;
  not?: InputMaybe<TdaRezfishPlatformFiltersInput>;
};

export type TdaRezfishPlatformInput = {
  name?: InputMaybe<Scalars['String']>;
  mentors?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaRezfishPlatform = {
  __typename?: 'TdaRezfishPlatform';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  mentors_connection?: Maybe<TdaRezfishMentorRelationResponseCollection>;
  mentors: Array<Maybe<TdaRezfishMentor>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaRezfishPlatformMentors_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishPlatformMentorsArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaRezfishPlatformEntityResponseCollection = {
  __typename?: 'TdaRezfishPlatformEntityResponseCollection';
  nodes: Array<TdaRezfishPlatform>;
  pageInfo: Pagination;
};

export type TdaRezfishTagFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  tagName?: InputMaybe<StringFilterInput>;
  mentors?: InputMaybe<TdaRezfishMentorFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaRezfishTagFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaRezfishTagFiltersInput>>>;
  not?: InputMaybe<TdaRezfishTagFiltersInput>;
};

export type TdaRezfishTagInput = {
  tagName?: InputMaybe<Scalars['String']>;
  mentors?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaRezfishTag = {
  __typename?: 'TdaRezfishTag';
  documentId: Scalars['ID'];
  tagName?: Maybe<Scalars['String']>;
  mentors_connection?: Maybe<TdaRezfishMentorRelationResponseCollection>;
  mentors: Array<Maybe<TdaRezfishMentor>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaRezfishTagMentors_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaRezfishTagMentorsArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaRezfishTagEntityResponseCollection = {
  __typename?: 'TdaRezfishTagEntityResponseCollection';
  nodes: Array<TdaRezfishTag>;
  pageInfo: Pagination;
};

export type TdaRezfishTagRelationResponseCollection = {
  __typename?: 'TdaRezfishTagRelationResponseCollection';
  nodes: Array<TdaRezfishTag>;
};

export type TdaSpechfishEvaluationGroupFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  code?: InputMaybe<StringFilterInput>;
  tda_evaluation_set?: InputMaybe<TdaSpechfishEvaluationSetFiltersInput>;
  tda_evaluation_questions?: InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>>>;
  not?: InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>;
};

export type TdaSpechfishEvaluationGroupInput = {
  name?: InputMaybe<Scalars['String']>;
  code?: InputMaybe<Scalars['String']>;
  tda_evaluation_set?: InputMaybe<Scalars['ID']>;
  tda_evaluation_questions?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaSpechfishEvaluationGroup = {
  __typename?: 'TdaSpechfishEvaluationGroup';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  code: Scalars['String'];
  tda_evaluation_set?: Maybe<TdaSpechfishEvaluationSet>;
  tda_evaluation_questions_connection?: Maybe<TdaSpechfishEvaluationQuestionRelationResponseCollection>;
  tda_evaluation_questions: Array<Maybe<TdaSpechfishEvaluationQuestion>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaSpechfishEvaluationGroupTda_Evaluation_Questions_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaSpechfishEvaluationGroupTda_Evaluation_QuestionsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaSpechfishEvaluationGroupEntityResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationGroupEntityResponseCollection';
  nodes: Array<TdaSpechfishEvaluationGroup>;
  pageInfo: Pagination;
};

export type TdaSpechfishEvaluationGroupRelationResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationGroupRelationResponseCollection';
  nodes: Array<TdaSpechfishEvaluationGroup>;
};

export type TdaSpechfishEvaluationQuestionFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  order?: InputMaybe<IntFilterInput>;
  isBonus?: InputMaybe<BooleanFilterInput>;
  points?: InputMaybe<FloatFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  tda_evaluation_group?: InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>;
  tda_evaluation_teams?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>>>;
  not?: InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>;
};

export type TdaSpechfishEvaluationQuestionInput = {
  name?: InputMaybe<Scalars['String']>;
  order?: InputMaybe<Scalars['Int']>;
  isBonus?: InputMaybe<Scalars['Boolean']>;
  points?: InputMaybe<Scalars['Float']>;
  content?: InputMaybe<Scalars['String']>;
  tda_evaluation_group?: InputMaybe<Scalars['ID']>;
  tda_evaluation_teams?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaSpechfishEvaluationQuestion = {
  __typename?: 'TdaSpechfishEvaluationQuestion';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  order: Scalars['Int'];
  isBonus: Scalars['Boolean'];
  points?: Maybe<Scalars['Float']>;
  content?: Maybe<Scalars['String']>;
  tda_evaluation_group?: Maybe<TdaSpechfishEvaluationGroup>;
  tda_evaluation_teams_connection?: Maybe<TdaSpechfishEvaluationTeamRelationResponseCollection>;
  tda_evaluation_teams: Array<Maybe<TdaSpechfishEvaluationTeam>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaSpechfishEvaluationQuestionTda_Evaluation_Teams_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaSpechfishEvaluationQuestionTda_Evaluation_TeamsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaSpechfishEvaluationQuestionEntityResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationQuestionEntityResponseCollection';
  nodes: Array<TdaSpechfishEvaluationQuestion>;
  pageInfo: Pagination;
};

export type TdaSpechfishEvaluationQuestionRelationResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationQuestionRelationResponseCollection';
  nodes: Array<TdaSpechfishEvaluationQuestion>;
};

export enum Enum_Tdaspechfishevaluationset_Type {
  Fx = 'FX',
  Ux = 'UX'
}

export type TdaSpechfishEvaluationSetFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  type?: InputMaybe<StringFilterInput>;
  tda_phase?: InputMaybe<TdaCorePhaseFiltersInput>;
  tda_evaluation_groups?: InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationSetFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationSetFiltersInput>>>;
  not?: InputMaybe<TdaSpechfishEvaluationSetFiltersInput>;
};

export type TdaSpechfishEvaluationSetInput = {
  type?: InputMaybe<Enum_Tdaspechfishevaluationset_Type>;
  tda_phase?: InputMaybe<Scalars['ID']>;
  tda_evaluation_groups?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaSpechfishEvaluationSet = {
  __typename?: 'TdaSpechfishEvaluationSet';
  documentId: Scalars['ID'];
  type: Enum_Tdaspechfishevaluationset_Type;
  tda_phase?: Maybe<TdaCorePhase>;
  tda_evaluation_groups_connection?: Maybe<TdaSpechfishEvaluationGroupRelationResponseCollection>;
  tda_evaluation_groups: Array<Maybe<TdaSpechfishEvaluationGroup>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaSpechfishEvaluationSetTda_Evaluation_Groups_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TdaSpechfishEvaluationSetTda_Evaluation_GroupsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaSpechfishEvaluationSetEntityResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationSetEntityResponseCollection';
  nodes: Array<TdaSpechfishEvaluationSet>;
  pageInfo: Pagination;
};

export type TdaSpechfishEvaluationSetRelationResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationSetRelationResponseCollection';
  nodes: Array<TdaSpechfishEvaluationSet>;
};

export type TdaSpechfishEvaluationTeamFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  gainedPoints?: InputMaybe<FloatFilterInput>;
  tda_evaluation_question?: InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>;
  user?: InputMaybe<UsersPermissionsUserFiltersInput>;
  tda_team?: InputMaybe<TdaCoreTeamFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>>>;
  not?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
};

export type TdaSpechfishEvaluationTeamInput = {
  gainedPoints?: InputMaybe<Scalars['Float']>;
  tda_evaluation_question?: InputMaybe<Scalars['ID']>;
  user?: InputMaybe<Scalars['ID']>;
  tda_team?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaSpechfishEvaluationTeam = {
  __typename?: 'TdaSpechfishEvaluationTeam';
  documentId: Scalars['ID'];
  gainedPoints: Scalars['Float'];
  tda_evaluation_question?: Maybe<TdaSpechfishEvaluationQuestion>;
  user?: Maybe<UsersPermissionsUser>;
  tda_team?: Maybe<TdaCoreTeam>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type TdaSpechfishEvaluationTeamEntityResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationTeamEntityResponseCollection';
  nodes: Array<TdaSpechfishEvaluationTeam>;
  pageInfo: Pagination;
};

export type TdaSpechfishEvaluationTeamRelationResponseCollection = {
  __typename?: 'TdaSpechfishEvaluationTeamRelationResponseCollection';
  nodes: Array<TdaSpechfishEvaluationTeam>;
};

export type TdaTdcApplicationFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  branch?: InputMaybe<StringFilterInput>;
  repository?: InputMaybe<StringFilterInput>;
  deployments?: InputMaybe<ComponentTdcDeploymentFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<TdaTdcApplicationFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<TdaTdcApplicationFiltersInput>>>;
  not?: InputMaybe<TdaTdcApplicationFiltersInput>;
};

export type TdaTdcApplicationInput = {
  name?: InputMaybe<Scalars['String']>;
  branch?: InputMaybe<Scalars['String']>;
  repository?: InputMaybe<Scalars['String']>;
  deployments?: InputMaybe<Array<InputMaybe<ComponentTdcDeploymentInput>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type TdaTdcApplication = {
  __typename?: 'TdaTdcApplication';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  branch?: Maybe<Scalars['String']>;
  repository?: Maybe<Scalars['String']>;
  deployments?: Maybe<Array<Maybe<ComponentTdcDeployment>>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type TdaTdcApplicationDeploymentsArgs = {
  filters?: InputMaybe<ComponentTdcDeploymentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type TdaTdcApplicationEntityResponseCollection = {
  __typename?: 'TdaTdcApplicationEntityResponseCollection';
  nodes: Array<TdaTdcApplication>;
  pageInfo: Pagination;
};

export type WebArticleFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  publishDate?: InputMaybe<DateTimeFilterInput>;
  unpublishDate?: InputMaybe<DateTimeFilterInput>;
  content?: InputMaybe<StringFilterInput>;
  hasFullHtmlControl?: InputMaybe<BooleanFilterInput>;
  showAuthor?: InputMaybe<BooleanFilterInput>;
  author?: InputMaybe<UsersPermissionsUserFiltersInput>;
  title?: InputMaybe<StringFilterInput>;
  anotation?: InputMaybe<StringFilterInput>;
  slug?: InputMaybe<StringFilterInput>;
  articleCategory?: InputMaybe<WebArticleCategoryFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebArticleFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebArticleFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebArticleFiltersInput>>>;
  not?: InputMaybe<WebArticleFiltersInput>;
};

export type WebArticleInput = {
  publishDate?: InputMaybe<Scalars['DateTime']>;
  unpublishDate?: InputMaybe<Scalars['DateTime']>;
  content?: InputMaybe<Scalars['String']>;
  hasFullHtmlControl?: InputMaybe<Scalars['Boolean']>;
  showAuthor?: InputMaybe<Scalars['Boolean']>;
  author?: InputMaybe<Scalars['ID']>;
  coverImage?: InputMaybe<Scalars['ID']>;
  title?: InputMaybe<Scalars['String']>;
  anotation?: InputMaybe<Scalars['String']>;
  slug?: InputMaybe<Scalars['String']>;
  articleCategory?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebArticle = {
  __typename?: 'WebArticle';
  documentId: Scalars['ID'];
  publishDate: Scalars['DateTime'];
  unpublishDate?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  hasFullHtmlControl: Scalars['Boolean'];
  showAuthor: Scalars['Boolean'];
  author?: Maybe<UsersPermissionsUser>;
  coverImage?: Maybe<UploadFile>;
  title: Scalars['String'];
  anotation?: Maybe<Scalars['String']>;
  slug: Scalars['String'];
  articleCategory?: Maybe<WebArticleCategory>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebArticleRelationResponseCollection>;
  localizations: Array<Maybe<WebArticle>>;
};


export type WebArticleLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebArticleLocalizationsArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebArticleEntityResponseCollection = {
  __typename?: 'WebArticleEntityResponseCollection';
  nodes: Array<WebArticle>;
  pageInfo: Pagination;
};

export type WebArticleRelationResponseCollection = {
  __typename?: 'WebArticleRelationResponseCollection';
  nodes: Array<WebArticle>;
};

export type WebArticleCategoryFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  articles?: InputMaybe<WebArticleFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebArticleCategoryFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebArticleCategoryFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebArticleCategoryFiltersInput>>>;
  not?: InputMaybe<WebArticleCategoryFiltersInput>;
};

export type WebArticleCategoryInput = {
  name?: InputMaybe<Scalars['String']>;
  defaultCoverImage?: InputMaybe<Scalars['ID']>;
  articles?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebArticleCategory = {
  __typename?: 'WebArticleCategory';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  defaultCoverImage: UploadFile;
  articles_connection?: Maybe<WebArticleRelationResponseCollection>;
  articles: Array<Maybe<WebArticle>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebArticleCategoryRelationResponseCollection>;
  localizations: Array<Maybe<WebArticleCategory>>;
};


export type WebArticleCategoryArticles_ConnectionArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebArticleCategoryArticlesArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebArticleCategoryLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebArticleCategoryLocalizationsArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebArticleCategoryEntityResponseCollection = {
  __typename?: 'WebArticleCategoryEntityResponseCollection';
  nodes: Array<WebArticleCategory>;
  pageInfo: Pagination;
};

export type WebArticleCategoryRelationResponseCollection = {
  __typename?: 'WebArticleCategoryRelationResponseCollection';
  nodes: Array<WebArticleCategory>;
};

export type WebContactFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  webContactGroup?: InputMaybe<WebContactGroupFiltersInput>;
  roleName?: InputMaybe<StringFilterInput>;
  user?: InputMaybe<UsersPermissionsUserFiltersInput>;
  weight?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebContactFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebContactFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebContactFiltersInput>>>;
  not?: InputMaybe<WebContactFiltersInput>;
};

export type WebContactInput = {
  webContactGroup?: InputMaybe<Scalars['ID']>;
  roleName?: InputMaybe<Scalars['String']>;
  user?: InputMaybe<Scalars['ID']>;
  weight?: InputMaybe<Scalars['Int']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebContact = {
  __typename?: 'WebContact';
  documentId: Scalars['ID'];
  webContactGroup?: Maybe<WebContactGroup>;
  roleName?: Maybe<Scalars['String']>;
  user?: Maybe<UsersPermissionsUser>;
  weight: Scalars['Int'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebContactRelationResponseCollection>;
  localizations: Array<Maybe<WebContact>>;
};


export type WebContactLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebContactLocalizationsArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebContactEntityResponseCollection = {
  __typename?: 'WebContactEntityResponseCollection';
  nodes: Array<WebContact>;
  pageInfo: Pagination;
};

export type WebContactRelationResponseCollection = {
  __typename?: 'WebContactRelationResponseCollection';
  nodes: Array<WebContact>;
};

export type WebContactGroupFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  weight?: InputMaybe<IntFilterInput>;
  webContacts?: InputMaybe<WebContactFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebContactGroupFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebContactGroupFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebContactGroupFiltersInput>>>;
  not?: InputMaybe<WebContactGroupFiltersInput>;
};

export type WebContactGroupInput = {
  name?: InputMaybe<Scalars['String']>;
  weight?: InputMaybe<Scalars['Int']>;
  webContacts?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebContactGroup = {
  __typename?: 'WebContactGroup';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  weight: Scalars['Int'];
  webContacts_connection?: Maybe<WebContactRelationResponseCollection>;
  webContacts: Array<Maybe<WebContact>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebContactGroupRelationResponseCollection>;
  localizations: Array<Maybe<WebContactGroup>>;
};


export type WebContactGroupWebContacts_ConnectionArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebContactGroupWebContactsArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebContactGroupLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebContactGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebContactGroupLocalizationsArgs = {
  filters?: InputMaybe<WebContactGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebContactGroupEntityResponseCollection = {
  __typename?: 'WebContactGroupEntityResponseCollection';
  nodes: Array<WebContactGroup>;
  pageInfo: Pagination;
};

export type WebContactGroupRelationResponseCollection = {
  __typename?: 'WebContactGroupRelationResponseCollection';
  nodes: Array<WebContactGroup>;
};

export enum Enum_Webmenuitem_Menutype {
  TopNav = 'top_nav',
  FrontPage = 'front_page',
  FooterNav = 'footer_nav'
}

export type WebMenuItemFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  text?: InputMaybe<StringFilterInput>;
  link?: InputMaybe<StringFilterInput>;
  isValid?: InputMaybe<BooleanFilterInput>;
  menuType?: InputMaybe<StringFilterInput>;
  weight?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebMenuItemFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebMenuItemFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebMenuItemFiltersInput>>>;
  not?: InputMaybe<WebMenuItemFiltersInput>;
};

export type WebMenuItemInput = {
  text?: InputMaybe<Scalars['String']>;
  link?: InputMaybe<Scalars['String']>;
  isValid?: InputMaybe<Scalars['Boolean']>;
  menuType?: InputMaybe<Enum_Webmenuitem_Menutype>;
  weight?: InputMaybe<Scalars['Int']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebMenuItem = {
  __typename?: 'WebMenuItem';
  documentId: Scalars['ID'];
  text: Scalars['String'];
  link: Scalars['String'];
  isValid: Scalars['Boolean'];
  menuType: Enum_Webmenuitem_Menutype;
  weight: Scalars['Int'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebMenuItemRelationResponseCollection>;
  localizations: Array<Maybe<WebMenuItem>>;
};


export type WebMenuItemLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebMenuItemFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebMenuItemLocalizationsArgs = {
  filters?: InputMaybe<WebMenuItemFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebMenuItemEntityResponseCollection = {
  __typename?: 'WebMenuItemEntityResponseCollection';
  nodes: Array<WebMenuItem>;
  pageInfo: Pagination;
};

export type WebMenuItemRelationResponseCollection = {
  __typename?: 'WebMenuItemRelationResponseCollection';
  nodes: Array<WebMenuItem>;
};

export enum Enum_Webpage_Redirecttype {
  Temporary = 'temporary',
  Permanent = 'permanent'
}

export type WebPageBlocksDynamicZone = ComponentContentBlocksHomeSplashScreen | ComponentContentBlocksBasicContent | ComponentContentBlocksPartners | ComponentContentBlocksContacts | ComponentContentBlocksRegistrationForm | ComponentContentBlocksRegistrationSuccess | ComponentContentBlocksArticleList | Error;

export type WebPageFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  slug?: InputMaybe<StringFilterInput>;
  title?: InputMaybe<StringFilterInput>;
  noContentRedirectUrl?: InputMaybe<StringFilterInput>;
  metaDescription?: InputMaybe<StringFilterInput>;
  redirectType?: InputMaybe<StringFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebPageFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebPageFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebPageFiltersInput>>>;
  not?: InputMaybe<WebPageFiltersInput>;
};

export type WebPageInput = {
  slug?: InputMaybe<Scalars['String']>;
  blocks?: InputMaybe<Array<Scalars['WebPageBlocksDynamicZoneInput']>>;
  title?: InputMaybe<Scalars['String']>;
  noContentRedirectUrl?: InputMaybe<Scalars['String']>;
  metaDescription?: InputMaybe<Scalars['String']>;
  redirectType?: InputMaybe<Enum_Webpage_Redirecttype>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebPage = {
  __typename?: 'WebPage';
  documentId: Scalars['ID'];
  slug?: Maybe<Scalars['String']>;
  blocks?: Maybe<Array<Maybe<WebPageBlocksDynamicZone>>>;
  title?: Maybe<Scalars['String']>;
  noContentRedirectUrl?: Maybe<Scalars['String']>;
  metaDescription?: Maybe<Scalars['String']>;
  redirectType: Enum_Webpage_Redirecttype;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebPageRelationResponseCollection>;
  localizations: Array<Maybe<WebPage>>;
};


export type WebPageLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebPageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebPageLocalizationsArgs = {
  filters?: InputMaybe<WebPageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebPageEntityResponseCollection = {
  __typename?: 'WebPageEntityResponseCollection';
  nodes: Array<WebPage>;
  pageInfo: Pagination;
};

export type WebPageRelationResponseCollection = {
  __typename?: 'WebPageRelationResponseCollection';
  nodes: Array<WebPage>;
};

export type WebPartnerFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  url?: InputMaybe<StringFilterInput>;
  text?: InputMaybe<StringFilterInput>;
  webPartnerGroup?: InputMaybe<WebPartnerGroupFiltersInput>;
  weight?: InputMaybe<IntFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebPartnerFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebPartnerFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebPartnerFiltersInput>>>;
  not?: InputMaybe<WebPartnerFiltersInput>;
};

export type WebPartnerInput = {
  name?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
  text?: InputMaybe<Scalars['String']>;
  logo?: InputMaybe<Scalars['ID']>;
  webPartnerGroup?: InputMaybe<Scalars['ID']>;
  weight?: InputMaybe<Scalars['Int']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebPartner = {
  __typename?: 'WebPartner';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  url: Scalars['String'];
  text: Scalars['String'];
  logo: UploadFile;
  webPartnerGroup?: Maybe<WebPartnerGroup>;
  weight: Scalars['Int'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebPartnerRelationResponseCollection>;
  localizations: Array<Maybe<WebPartner>>;
};


export type WebPartnerLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebPartnerLocalizationsArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebPartnerEntityResponseCollection = {
  __typename?: 'WebPartnerEntityResponseCollection';
  nodes: Array<WebPartner>;
  pageInfo: Pagination;
};

export type WebPartnerRelationResponseCollection = {
  __typename?: 'WebPartnerRelationResponseCollection';
  nodes: Array<WebPartner>;
};

export enum Enum_Webpartnergroup_Importance {
  High = 'high',
  Medium = 'medium',
  Low = 'low'
}

export type WebPartnerGroupFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  weight?: InputMaybe<IntFilterInput>;
  importance?: InputMaybe<StringFilterInput>;
  webPartners?: InputMaybe<WebPartnerFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  locale?: InputMaybe<StringFilterInput>;
  localizations?: InputMaybe<WebPartnerGroupFiltersInput>;
  and?: InputMaybe<Array<InputMaybe<WebPartnerGroupFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<WebPartnerGroupFiltersInput>>>;
  not?: InputMaybe<WebPartnerGroupFiltersInput>;
};

export type WebPartnerGroupInput = {
  name?: InputMaybe<Scalars['String']>;
  weight?: InputMaybe<Scalars['Int']>;
  importance?: InputMaybe<Enum_Webpartnergroup_Importance>;
  webPartners?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type WebPartnerGroup = {
  __typename?: 'WebPartnerGroup';
  documentId: Scalars['ID'];
  name: Scalars['String'];
  weight: Scalars['Int'];
  importance: Enum_Webpartnergroup_Importance;
  webPartners_connection?: Maybe<WebPartnerRelationResponseCollection>;
  webPartners: Array<Maybe<WebPartner>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
  locale?: Maybe<Scalars['String']>;
  localizations_connection?: Maybe<WebPartnerGroupRelationResponseCollection>;
  localizations: Array<Maybe<WebPartnerGroup>>;
};


export type WebPartnerGroupWebPartners_ConnectionArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebPartnerGroupWebPartnersArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebPartnerGroupLocalizations_ConnectionArgs = {
  filters?: InputMaybe<WebPartnerGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type WebPartnerGroupLocalizationsArgs = {
  filters?: InputMaybe<WebPartnerGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type WebPartnerGroupEntityResponseCollection = {
  __typename?: 'WebPartnerGroupEntityResponseCollection';
  nodes: Array<WebPartnerGroup>;
  pageInfo: Pagination;
};

export type WebPartnerGroupRelationResponseCollection = {
  __typename?: 'WebPartnerGroupRelationResponseCollection';
  nodes: Array<WebPartnerGroup>;
};

export type XoGhostProjectFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  ghostId?: InputMaybe<StringFilterInput>;
  xo_ghost_tournaments?: InputMaybe<XoGhostTournamentFiltersInput>;
  xo_ghost_teams?: InputMaybe<XoGhostTeamFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<XoGhostProjectFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<XoGhostProjectFiltersInput>>>;
  not?: InputMaybe<XoGhostProjectFiltersInput>;
};

export type XoGhostProjectInput = {
  name?: InputMaybe<Scalars['String']>;
  ghostId?: InputMaybe<Scalars['String']>;
  xo_ghost_tournaments?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  xo_ghost_teams?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type XoGhostProject = {
  __typename?: 'XoGhostProject';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  ghostId?: Maybe<Scalars['String']>;
  xo_ghost_tournaments_connection?: Maybe<XoGhostTournamentRelationResponseCollection>;
  xo_ghost_tournaments: Array<Maybe<XoGhostTournament>>;
  xo_ghost_teams_connection?: Maybe<XoGhostTeamRelationResponseCollection>;
  xo_ghost_teams: Array<Maybe<XoGhostTeam>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type XoGhostProjectXo_Ghost_Tournaments_ConnectionArgs = {
  filters?: InputMaybe<XoGhostTournamentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type XoGhostProjectXo_Ghost_TournamentsArgs = {
  filters?: InputMaybe<XoGhostTournamentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type XoGhostProjectXo_Ghost_Teams_ConnectionArgs = {
  filters?: InputMaybe<XoGhostTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type XoGhostProjectXo_Ghost_TeamsArgs = {
  filters?: InputMaybe<XoGhostTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type XoGhostProjectEntityResponseCollection = {
  __typename?: 'XoGhostProjectEntityResponseCollection';
  nodes: Array<XoGhostProject>;
  pageInfo: Pagination;
};

export type XoGhostSchoolFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  shortName?: InputMaybe<StringFilterInput>;
  city?: InputMaybe<StringFilterInput>;
  street?: InputMaybe<StringFilterInput>;
  postalCode?: InputMaybe<StringFilterInput>;
  region?: InputMaybe<StringFilterInput>;
  ghostTeam?: InputMaybe<XoGhostTeamFiltersInput>;
  ghostTournaments?: InputMaybe<XoGhostTournamentFiltersInput>;
  lat?: InputMaybe<FloatFilterInput>;
  long?: InputMaybe<FloatFilterInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<XoGhostSchoolFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<XoGhostSchoolFiltersInput>>>;
  not?: InputMaybe<XoGhostSchoolFiltersInput>;
};

export type XoGhostSchoolInput = {
  name?: InputMaybe<Scalars['String']>;
  shortName?: InputMaybe<Scalars['String']>;
  city?: InputMaybe<Scalars['String']>;
  street?: InputMaybe<Scalars['String']>;
  postalCode?: InputMaybe<Scalars['String']>;
  region?: InputMaybe<Scalars['String']>;
  ghostTeam?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  ghostTournaments?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  lat?: InputMaybe<Scalars['Float']>;
  long?: InputMaybe<Scalars['Float']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type XoGhostSchool = {
  __typename?: 'XoGhostSchool';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  shortName?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  street?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  region?: Maybe<Scalars['String']>;
  ghostTeam_connection?: Maybe<XoGhostTeamRelationResponseCollection>;
  ghostTeam: Array<Maybe<XoGhostTeam>>;
  ghostTournaments_connection?: Maybe<XoGhostTournamentRelationResponseCollection>;
  ghostTournaments: Array<Maybe<XoGhostTournament>>;
  lat?: Maybe<Scalars['Float']>;
  long?: Maybe<Scalars['Float']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};


export type XoGhostSchoolGhostTeam_ConnectionArgs = {
  filters?: InputMaybe<XoGhostTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type XoGhostSchoolGhostTeamArgs = {
  filters?: InputMaybe<XoGhostTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type XoGhostSchoolGhostTournaments_ConnectionArgs = {
  filters?: InputMaybe<XoGhostTournamentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type XoGhostSchoolGhostTournamentsArgs = {
  filters?: InputMaybe<XoGhostTournamentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type XoGhostSchoolEntityResponseCollection = {
  __typename?: 'XoGhostSchoolEntityResponseCollection';
  nodes: Array<XoGhostSchool>;
  pageInfo: Pagination;
};

export type XoGhostTeamFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  ghostSchool?: InputMaybe<XoGhostSchoolFiltersInput>;
  project?: InputMaybe<XoGhostProjectFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<XoGhostTeamFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<XoGhostTeamFiltersInput>>>;
  not?: InputMaybe<XoGhostTeamFiltersInput>;
};

export type XoGhostTeamInput = {
  name?: InputMaybe<Scalars['String']>;
  ghostSchool?: InputMaybe<Scalars['ID']>;
  project?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type XoGhostTeam = {
  __typename?: 'XoGhostTeam';
  documentId: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  ghostSchool?: Maybe<XoGhostSchool>;
  project?: Maybe<XoGhostProject>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type XoGhostTeamEntityResponseCollection = {
  __typename?: 'XoGhostTeamEntityResponseCollection';
  nodes: Array<XoGhostTeam>;
  pageInfo: Pagination;
};

export type XoGhostTeamRelationResponseCollection = {
  __typename?: 'XoGhostTeamRelationResponseCollection';
  nodes: Array<XoGhostTeam>;
};

export type XoGhostTournamentFiltersInput = {
  documentId?: InputMaybe<IdFilterInput>;
  groupOfActions?: InputMaybe<StringFilterInput>;
  name?: InputMaybe<StringFilterInput>;
  koo?: InputMaybe<StringFilterInput>;
  ghostSchool?: InputMaybe<XoGhostSchoolFiltersInput>;
  places?: InputMaybe<StringFilterInput>;
  project?: InputMaybe<XoGhostProjectFiltersInput>;
  createdAt?: InputMaybe<DateTimeFilterInput>;
  updatedAt?: InputMaybe<DateTimeFilterInput>;
  publishedAt?: InputMaybe<DateTimeFilterInput>;
  and?: InputMaybe<Array<InputMaybe<XoGhostTournamentFiltersInput>>>;
  or?: InputMaybe<Array<InputMaybe<XoGhostTournamentFiltersInput>>>;
  not?: InputMaybe<XoGhostTournamentFiltersInput>;
};

export type XoGhostTournamentInput = {
  groupOfActions?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  koo?: InputMaybe<Scalars['String']>;
  ghostSchool?: InputMaybe<Scalars['ID']>;
  places?: InputMaybe<Scalars['String']>;
  project?: InputMaybe<Scalars['ID']>;
  publishedAt?: InputMaybe<Scalars['DateTime']>;
};

export type XoGhostTournament = {
  __typename?: 'XoGhostTournament';
  documentId: Scalars['ID'];
  groupOfActions?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  koo?: Maybe<Scalars['String']>;
  ghostSchool?: Maybe<XoGhostSchool>;
  places?: Maybe<Scalars['String']>;
  project?: Maybe<XoGhostProject>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  publishedAt?: Maybe<Scalars['DateTime']>;
};

export type XoGhostTournamentEntityResponseCollection = {
  __typename?: 'XoGhostTournamentEntityResponseCollection';
  nodes: Array<XoGhostTournament>;
  pageInfo: Pagination;
};

export type XoGhostTournamentRelationResponseCollection = {
  __typename?: 'XoGhostTournamentRelationResponseCollection';
  nodes: Array<XoGhostTournament>;
};

export type GenericMorph = ComponentTdcDeployment | ComponentTdaRezfishMeetComponentsTimeBlock | ComponentTdaRezfishMeetComponentsAvailableTime | ComponentTdaFisherPhasesResult | ComponentTdaFisherPhasesLink | ComponentTdaFisherPhasesInfo | ComponentTdaFisherPhasesInfoPanel | ComponentTdaCtfComponentsStickyNote | ComponentTdaCtfComponentsGenericNote | ComponentTdaCtfComponentsEmail | ComponentMultimediaBlocksFormCheckField | ComponentMultimediaBlocksCroppedImage | ComponentMailingListOfSentEMails | ComponentMailingFullEMail | ComponentDiscordDiscordIdList | ComponentContentBlocksRegistrationSuccess | ComponentContentBlocksRegistrationForm | ComponentContentBlocksPartners | ComponentContentBlocksHomeSplashScreen | ComponentContentBlocksContacts | ComponentContentBlocksBasicContent | ComponentContentBlocksArticleList | UploadFile | I18NLocale | ReviewWorkflowsWorkflow | ReviewWorkflowsWorkflowStage | StrapiPluginSsoRoles | StrapiPluginSsoWhitelists | UsersPermissionsPermission | UsersPermissionsRole | UsersPermissionsUser | CoreContestant | CoreEvent | CoreEventGroup | CoreProject | CoreProjectType | CoreRegion | CoreSchool | CoreSchoolContact | CoreSchoolType | Dummy | MailingBatch | MailingBlacklist | MailingSource | MailingTemplate | MailingTheme | ScgBotConfig | ScgBotSuggestion | TdaBot | TdaBotContestant | TdaBotTag | TdaBusherTeamTest | TdaBusherTest | TdaCorePhase | TdaCoreTeam | TdaFisherFlag | TdaFisherTeamFlag | TdaFisherTeamResult | TdaRezfishMeet | TdaRezfishMeetCategory | TdaRezfishMeetLength | TdaRezfishMentor | TdaRezfishPlatform | TdaRezfishTag | TdaSpechfishEvaluationGroup | TdaSpechfishEvaluationQuestion | TdaSpechfishEvaluationSet | TdaSpechfishEvaluationTeam | TdaTdcApplication | WebArticle | WebArticleCategory | WebContact | WebContactGroup | WebMenuItem | WebPage | WebPartner | WebPartnerGroup | XoGhostProject | XoGhostSchool | XoGhostTeam | XoGhostTournament;

export type FileInfoInput = {
  name?: InputMaybe<Scalars['String']>;
  alternativeText?: InputMaybe<Scalars['String']>;
  caption?: InputMaybe<Scalars['String']>;
};

export type UsersPermissionsMe = {
  __typename?: 'UsersPermissionsMe';
  id: Scalars['ID'];
  documentId: Scalars['ID'];
  username: Scalars['String'];
  email?: Maybe<Scalars['String']>;
  confirmed?: Maybe<Scalars['Boolean']>;
  blocked?: Maybe<Scalars['Boolean']>;
  role?: Maybe<UsersPermissionsMeRole>;
};

export type UsersPermissionsMeRole = {
  __typename?: 'UsersPermissionsMeRole';
  id: Scalars['ID'];
  name: Scalars['String'];
  description?: Maybe<Scalars['String']>;
  type?: Maybe<Scalars['String']>;
};

export type UsersPermissionsRegisterInput = {
  username: Scalars['String'];
  email: Scalars['String'];
  password: Scalars['String'];
};

export type UsersPermissionsLoginInput = {
  identifier: Scalars['String'];
  password: Scalars['String'];
  provider?: Scalars['String'];
};

export type UsersPermissionsPasswordPayload = {
  __typename?: 'UsersPermissionsPasswordPayload';
  ok: Scalars['Boolean'];
};

export type UsersPermissionsLoginPayload = {
  __typename?: 'UsersPermissionsLoginPayload';
  jwt?: Maybe<Scalars['String']>;
  user: UsersPermissionsMe;
};

export type UsersPermissionsCreateRolePayload = {
  __typename?: 'UsersPermissionsCreateRolePayload';
  ok: Scalars['Boolean'];
};

export type UsersPermissionsUpdateRolePayload = {
  __typename?: 'UsersPermissionsUpdateRolePayload';
  ok: Scalars['Boolean'];
};

export type UsersPermissionsDeleteRolePayload = {
  __typename?: 'UsersPermissionsDeleteRolePayload';
  ok: Scalars['Boolean'];
};

export type PaginationArg = {
  page?: InputMaybe<Scalars['Int']>;
  pageSize?: InputMaybe<Scalars['Int']>;
  start?: InputMaybe<Scalars['Int']>;
  limit?: InputMaybe<Scalars['Int']>;
};

export type Query = {
  __typename?: 'Query';
  uploadFile?: Maybe<UploadFile>;
  uploadFiles_connection?: Maybe<UploadFileEntityResponseCollection>;
  uploadFiles: Array<Maybe<UploadFile>>;
  i18NLocale?: Maybe<I18NLocale>;
  i18NLocales_connection?: Maybe<I18NLocaleEntityResponseCollection>;
  i18NLocales: Array<Maybe<I18NLocale>>;
  reviewWorkflowsWorkflow?: Maybe<ReviewWorkflowsWorkflow>;
  reviewWorkflowsWorkflows_connection?: Maybe<ReviewWorkflowsWorkflowEntityResponseCollection>;
  reviewWorkflowsWorkflows: Array<Maybe<ReviewWorkflowsWorkflow>>;
  reviewWorkflowsWorkflowStage?: Maybe<ReviewWorkflowsWorkflowStage>;
  reviewWorkflowsWorkflowStages_connection?: Maybe<ReviewWorkflowsWorkflowStageEntityResponseCollection>;
  reviewWorkflowsWorkflowStages: Array<Maybe<ReviewWorkflowsWorkflowStage>>;
  strapiPluginSsoRoles?: Maybe<StrapiPluginSsoRoles>;
  strapiPluginSsoSsoRoles_connection?: Maybe<StrapiPluginSsoRolesEntityResponseCollection>;
  strapiPluginSsoSsoRoles: Array<Maybe<StrapiPluginSsoRoles>>;
  strapiPluginSsoWhitelists: Array<Maybe<StrapiPluginSsoWhitelists>>;
  strapiPluginSsoWhitelists_connection?: Maybe<StrapiPluginSsoWhitelistsEntityResponseCollection>;
  usersPermissionsRole?: Maybe<UsersPermissionsRole>;
  usersPermissionsRoles_connection?: Maybe<UsersPermissionsRoleEntityResponseCollection>;
  usersPermissionsRoles: Array<Maybe<UsersPermissionsRole>>;
  usersPermissionsUser?: Maybe<UsersPermissionsUser>;
  usersPermissionsUsers_connection?: Maybe<UsersPermissionsUserEntityResponseCollection>;
  usersPermissionsUsers: Array<Maybe<UsersPermissionsUser>>;
  coreContestant?: Maybe<CoreContestant>;
  coreContestants_connection?: Maybe<CoreContestantEntityResponseCollection>;
  coreContestants: Array<Maybe<CoreContestant>>;
  coreEvent?: Maybe<CoreEvent>;
  coreEvents_connection?: Maybe<CoreEventEntityResponseCollection>;
  coreEvents: Array<Maybe<CoreEvent>>;
  coreEventGroup?: Maybe<CoreEventGroup>;
  coreEventGroups_connection?: Maybe<CoreEventGroupEntityResponseCollection>;
  coreEventGroups: Array<Maybe<CoreEventGroup>>;
  coreProject?: Maybe<CoreProject>;
  coreProjects_connection?: Maybe<CoreProjectEntityResponseCollection>;
  coreProjects: Array<Maybe<CoreProject>>;
  coreProjectType?: Maybe<CoreProjectType>;
  coreProjectTypes_connection?: Maybe<CoreProjectTypeEntityResponseCollection>;
  coreProjectTypes: Array<Maybe<CoreProjectType>>;
  coreRegion?: Maybe<CoreRegion>;
  coreRegions_connection?: Maybe<CoreRegionEntityResponseCollection>;
  coreRegions: Array<Maybe<CoreRegion>>;
  coreSchool?: Maybe<CoreSchool>;
  coreSchools_connection?: Maybe<CoreSchoolEntityResponseCollection>;
  coreSchools: Array<Maybe<CoreSchool>>;
  coreSchoolContact?: Maybe<CoreSchoolContact>;
  coreSchoolContacts_connection?: Maybe<CoreSchoolContactEntityResponseCollection>;
  coreSchoolContacts: Array<Maybe<CoreSchoolContact>>;
  coreSchoolType?: Maybe<CoreSchoolType>;
  coreSchoolTypes_connection?: Maybe<CoreSchoolTypeEntityResponseCollection>;
  coreSchoolTypes: Array<Maybe<CoreSchoolType>>;
  dummy?: Maybe<Dummy>;
  dummies_connection?: Maybe<DummyEntityResponseCollection>;
  dummies: Array<Maybe<Dummy>>;
  mailingBatch?: Maybe<MailingBatch>;
  mailingBatches_connection?: Maybe<MailingBatchEntityResponseCollection>;
  mailingBatches: Array<Maybe<MailingBatch>>;
  mailingBlacklist?: Maybe<MailingBlacklist>;
  mailingSource?: Maybe<MailingSource>;
  mailingSources_connection?: Maybe<MailingSourceEntityResponseCollection>;
  mailingSources: Array<Maybe<MailingSource>>;
  mailingTemplate?: Maybe<MailingTemplate>;
  mailingTemplates_connection?: Maybe<MailingTemplateEntityResponseCollection>;
  mailingTemplates: Array<Maybe<MailingTemplate>>;
  mailingTheme?: Maybe<MailingTheme>;
  mailingThemes_connection?: Maybe<MailingThemeEntityResponseCollection>;
  mailingThemes: Array<Maybe<MailingTheme>>;
  scgBotConfig?: Maybe<ScgBotConfig>;
  scgBotSuggestion?: Maybe<ScgBotSuggestion>;
  scgBotSuggestions_connection?: Maybe<ScgBotSuggestionEntityResponseCollection>;
  scgBotSuggestions: Array<Maybe<ScgBotSuggestion>>;
  tdaBot?: Maybe<TdaBot>;
  tdaBotContestant?: Maybe<TdaBotContestant>;
  tdaBotContestants_connection?: Maybe<TdaBotContestantEntityResponseCollection>;
  tdaBotContestants: Array<Maybe<TdaBotContestant>>;
  tdaBotTag?: Maybe<TdaBotTag>;
  tdaBotTags_connection?: Maybe<TdaBotTagEntityResponseCollection>;
  tdaBotTags: Array<Maybe<TdaBotTag>>;
  tdaBusherTeamTest?: Maybe<TdaBusherTeamTest>;
  tdaBusherTeamTests_connection?: Maybe<TdaBusherTeamTestEntityResponseCollection>;
  tdaBusherTeamTests: Array<Maybe<TdaBusherTeamTest>>;
  tdaBusherTest?: Maybe<TdaBusherTest>;
  tdaBusherTests_connection?: Maybe<TdaBusherTestEntityResponseCollection>;
  tdaBusherTests: Array<Maybe<TdaBusherTest>>;
  tdaCorePhase?: Maybe<TdaCorePhase>;
  tdaCorePhases_connection?: Maybe<TdaCorePhaseEntityResponseCollection>;
  tdaCorePhases: Array<Maybe<TdaCorePhase>>;
  tdaCoreTeam?: Maybe<TdaCoreTeam>;
  tdaCoreTeams_connection?: Maybe<TdaCoreTeamEntityResponseCollection>;
  tdaCoreTeams: Array<Maybe<TdaCoreTeam>>;
  tdaFisherFlag?: Maybe<TdaFisherFlag>;
  tdaFisherFlags_connection?: Maybe<TdaFisherFlagEntityResponseCollection>;
  tdaFisherFlags: Array<Maybe<TdaFisherFlag>>;
  tdaFisherTeamFlag?: Maybe<TdaFisherTeamFlag>;
  tdaFisherTeamFlags_connection?: Maybe<TdaFisherTeamFlagEntityResponseCollection>;
  tdaFisherTeamFlags: Array<Maybe<TdaFisherTeamFlag>>;
  tdaFisherTeamResult?: Maybe<TdaFisherTeamResult>;
  tdaFisherTeamResults_connection?: Maybe<TdaFisherTeamResultEntityResponseCollection>;
  tdaFisherTeamResults: Array<Maybe<TdaFisherTeamResult>>;
  tdaRezfishMeet?: Maybe<TdaRezfishMeet>;
  tdaRezfishMeets_connection?: Maybe<TdaRezfishMeetEntityResponseCollection>;
  tdaRezfishMeets: Array<Maybe<TdaRezfishMeet>>;
  tdaRezfishMeetCategory?: Maybe<TdaRezfishMeetCategory>;
  tdaRezfishMeetCategories_connection?: Maybe<TdaRezfishMeetCategoryEntityResponseCollection>;
  tdaRezfishMeetCategories: Array<Maybe<TdaRezfishMeetCategory>>;
  tdaRezfishMeetLength?: Maybe<TdaRezfishMeetLength>;
  tdaRezfishMeetLengths_connection?: Maybe<TdaRezfishMeetLengthEntityResponseCollection>;
  tdaRezfishMeetLengths: Array<Maybe<TdaRezfishMeetLength>>;
  tdaRezfishMentor?: Maybe<TdaRezfishMentor>;
  tdaRezfishMentors_connection?: Maybe<TdaRezfishMentorEntityResponseCollection>;
  tdaRezfishMentors: Array<Maybe<TdaRezfishMentor>>;
  tdaRezfishPlatform?: Maybe<TdaRezfishPlatform>;
  tdaRezfishPlatforms_connection?: Maybe<TdaRezfishPlatformEntityResponseCollection>;
  tdaRezfishPlatforms: Array<Maybe<TdaRezfishPlatform>>;
  tdaRezfishTag?: Maybe<TdaRezfishTag>;
  tdaRezfishTags_connection?: Maybe<TdaRezfishTagEntityResponseCollection>;
  tdaRezfishTags: Array<Maybe<TdaRezfishTag>>;
  tdaSpechfishEvaluationGroup?: Maybe<TdaSpechfishEvaluationGroup>;
  tdaSpechfishEvaluationGroups_connection?: Maybe<TdaSpechfishEvaluationGroupEntityResponseCollection>;
  tdaSpechfishEvaluationGroups: Array<Maybe<TdaSpechfishEvaluationGroup>>;
  tdaSpechfishEvaluationQuestion?: Maybe<TdaSpechfishEvaluationQuestion>;
  tdaSpechfishEvaluationQuestions_connection?: Maybe<TdaSpechfishEvaluationQuestionEntityResponseCollection>;
  tdaSpechfishEvaluationQuestions: Array<Maybe<TdaSpechfishEvaluationQuestion>>;
  tdaSpechfishEvaluationSet?: Maybe<TdaSpechfishEvaluationSet>;
  tdaSpechfishEvaluationSets_connection?: Maybe<TdaSpechfishEvaluationSetEntityResponseCollection>;
  tdaSpechfishEvaluationSets: Array<Maybe<TdaSpechfishEvaluationSet>>;
  tdaSpechfishEvaluationTeam?: Maybe<TdaSpechfishEvaluationTeam>;
  tdaSpechfishEvaluationTeams_connection?: Maybe<TdaSpechfishEvaluationTeamEntityResponseCollection>;
  tdaSpechfishEvaluationTeams: Array<Maybe<TdaSpechfishEvaluationTeam>>;
  tdaTdcApplication?: Maybe<TdaTdcApplication>;
  tdaTdcApplications_connection?: Maybe<TdaTdcApplicationEntityResponseCollection>;
  tdaTdcApplications: Array<Maybe<TdaTdcApplication>>;
  webArticle?: Maybe<WebArticle>;
  webArticles_connection?: Maybe<WebArticleEntityResponseCollection>;
  webArticles: Array<Maybe<WebArticle>>;
  webArticleCategory?: Maybe<WebArticleCategory>;
  webArticleCategories_connection?: Maybe<WebArticleCategoryEntityResponseCollection>;
  webArticleCategories: Array<Maybe<WebArticleCategory>>;
  webContact?: Maybe<WebContact>;
  webContacts_connection?: Maybe<WebContactEntityResponseCollection>;
  webContacts: Array<Maybe<WebContact>>;
  webContactGroup?: Maybe<WebContactGroup>;
  webContactGroups_connection?: Maybe<WebContactGroupEntityResponseCollection>;
  webContactGroups: Array<Maybe<WebContactGroup>>;
  webMenuItem?: Maybe<WebMenuItem>;
  webMenuItems_connection?: Maybe<WebMenuItemEntityResponseCollection>;
  webMenuItems: Array<Maybe<WebMenuItem>>;
  webPage?: Maybe<WebPage>;
  webPages_connection?: Maybe<WebPageEntityResponseCollection>;
  webPages: Array<Maybe<WebPage>>;
  webPartner?: Maybe<WebPartner>;
  webPartners_connection?: Maybe<WebPartnerEntityResponseCollection>;
  webPartners: Array<Maybe<WebPartner>>;
  webPartnerGroup?: Maybe<WebPartnerGroup>;
  webPartnerGroups_connection?: Maybe<WebPartnerGroupEntityResponseCollection>;
  webPartnerGroups: Array<Maybe<WebPartnerGroup>>;
  xoGhostProject?: Maybe<XoGhostProject>;
  xoGhostProjects_connection?: Maybe<XoGhostProjectEntityResponseCollection>;
  xoGhostProjects: Array<Maybe<XoGhostProject>>;
  xoGhostSchool?: Maybe<XoGhostSchool>;
  xoGhostSchools_connection?: Maybe<XoGhostSchoolEntityResponseCollection>;
  xoGhostSchools: Array<Maybe<XoGhostSchool>>;
  xoGhostTeam?: Maybe<XoGhostTeam>;
  xoGhostTeams_connection?: Maybe<XoGhostTeamEntityResponseCollection>;
  xoGhostTeams: Array<Maybe<XoGhostTeam>>;
  xoGhostTournament?: Maybe<XoGhostTournament>;
  xoGhostTournaments_connection?: Maybe<XoGhostTournamentEntityResponseCollection>;
  xoGhostTournaments: Array<Maybe<XoGhostTournament>>;
  me?: Maybe<UsersPermissionsMe>;
};


export type QueryUploadFileArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUploadFiles_ConnectionArgs = {
  filters?: InputMaybe<UploadFileFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUploadFilesArgs = {
  filters?: InputMaybe<UploadFileFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryI18NLocaleArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryI18NLocales_ConnectionArgs = {
  filters?: InputMaybe<I18NLocaleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryI18NLocalesArgs = {
  filters?: InputMaybe<I18NLocaleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryReviewWorkflowsWorkflowArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryReviewWorkflowsWorkflows_ConnectionArgs = {
  filters?: InputMaybe<ReviewWorkflowsWorkflowFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryReviewWorkflowsWorkflowsArgs = {
  filters?: InputMaybe<ReviewWorkflowsWorkflowFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryReviewWorkflowsWorkflowStageArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryReviewWorkflowsWorkflowStages_ConnectionArgs = {
  filters?: InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryReviewWorkflowsWorkflowStagesArgs = {
  filters?: InputMaybe<ReviewWorkflowsWorkflowStageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryStrapiPluginSsoRolesArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryStrapiPluginSsoSsoRoles_ConnectionArgs = {
  filters?: InputMaybe<StrapiPluginSsoRolesFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryStrapiPluginSsoSsoRolesArgs = {
  filters?: InputMaybe<StrapiPluginSsoRolesFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryStrapiPluginSsoWhitelistsArgs = {
  filters?: InputMaybe<StrapiPluginSsoWhitelistsFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryStrapiPluginSsoWhitelists_ConnectionArgs = {
  filters?: InputMaybe<StrapiPluginSsoWhitelistsFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUsersPermissionsRoleArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUsersPermissionsRoles_ConnectionArgs = {
  filters?: InputMaybe<UsersPermissionsRoleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUsersPermissionsRolesArgs = {
  filters?: InputMaybe<UsersPermissionsRoleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUsersPermissionsUserArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUsersPermissionsUsers_ConnectionArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryUsersPermissionsUsersArgs = {
  filters?: InputMaybe<UsersPermissionsUserFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreContestantArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreContestants_ConnectionArgs = {
  filters?: InputMaybe<CoreContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreContestantsArgs = {
  filters?: InputMaybe<CoreContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreEventArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreEvents_ConnectionArgs = {
  filters?: InputMaybe<CoreEventFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreEventsArgs = {
  filters?: InputMaybe<CoreEventFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreEventGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreEventGroups_ConnectionArgs = {
  filters?: InputMaybe<CoreEventGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreEventGroupsArgs = {
  filters?: InputMaybe<CoreEventGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreProjectArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreProjects_ConnectionArgs = {
  filters?: InputMaybe<CoreProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreProjectsArgs = {
  filters?: InputMaybe<CoreProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreProjectTypeArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreProjectTypes_ConnectionArgs = {
  filters?: InputMaybe<CoreProjectTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreProjectTypesArgs = {
  filters?: InputMaybe<CoreProjectTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreRegionArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreRegions_ConnectionArgs = {
  filters?: InputMaybe<CoreRegionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreRegionsArgs = {
  filters?: InputMaybe<CoreRegionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchools_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolsArgs = {
  filters?: InputMaybe<CoreSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolContactArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolContacts_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolContactsArgs = {
  filters?: InputMaybe<CoreSchoolContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolTypeArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolTypes_ConnectionArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryCoreSchoolTypesArgs = {
  filters?: InputMaybe<CoreSchoolTypeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryDummyArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryDummies_ConnectionArgs = {
  filters?: InputMaybe<DummyFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryDummiesArgs = {
  filters?: InputMaybe<DummyFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingBatchArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingBatches_ConnectionArgs = {
  filters?: InputMaybe<MailingBatchFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingBatchesArgs = {
  filters?: InputMaybe<MailingBatchFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingBlacklistArgs = {
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingSourceArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingSources_ConnectionArgs = {
  filters?: InputMaybe<MailingSourceFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingSourcesArgs = {
  filters?: InputMaybe<MailingSourceFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingTemplateArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryMailingTemplates_ConnectionArgs = {
  filters?: InputMaybe<MailingTemplateFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryMailingTemplatesArgs = {
  filters?: InputMaybe<MailingTemplateFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryMailingThemeArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingThemes_ConnectionArgs = {
  filters?: InputMaybe<MailingThemeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryMailingThemesArgs = {
  filters?: InputMaybe<MailingThemeFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryScgBotConfigArgs = {
  status?: InputMaybe<PublicationStatus>;
};


export type QueryScgBotSuggestionArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryScgBotSuggestions_ConnectionArgs = {
  filters?: InputMaybe<ScgBotSuggestionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryScgBotSuggestionsArgs = {
  filters?: InputMaybe<ScgBotSuggestionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBotArgs = {
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBotContestantArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBotContestants_ConnectionArgs = {
  filters?: InputMaybe<TdaBotContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBotContestantsArgs = {
  filters?: InputMaybe<TdaBotContestantFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBotTagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBotTags_ConnectionArgs = {
  filters?: InputMaybe<TdaBotTagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBotTagsArgs = {
  filters?: InputMaybe<TdaBotTagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBusherTeamTestArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBusherTeamTests_ConnectionArgs = {
  filters?: InputMaybe<TdaBusherTeamTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBusherTeamTestsArgs = {
  filters?: InputMaybe<TdaBusherTeamTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBusherTestArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBusherTests_ConnectionArgs = {
  filters?: InputMaybe<TdaBusherTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaBusherTestsArgs = {
  filters?: InputMaybe<TdaBusherTestFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaCorePhaseArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaCorePhases_ConnectionArgs = {
  filters?: InputMaybe<TdaCorePhaseFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaCorePhasesArgs = {
  filters?: InputMaybe<TdaCorePhaseFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaCoreTeamArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaCoreTeams_ConnectionArgs = {
  filters?: InputMaybe<TdaCoreTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaCoreTeamsArgs = {
  filters?: InputMaybe<TdaCoreTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherFlagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherFlags_ConnectionArgs = {
  filters?: InputMaybe<TdaFisherFlagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherFlagsArgs = {
  filters?: InputMaybe<TdaFisherFlagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherTeamFlagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherTeamFlags_ConnectionArgs = {
  filters?: InputMaybe<TdaFisherTeamFlagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherTeamFlagsArgs = {
  filters?: InputMaybe<TdaFisherTeamFlagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherTeamResultArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherTeamResults_ConnectionArgs = {
  filters?: InputMaybe<TdaFisherTeamResultFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaFisherTeamResultsArgs = {
  filters?: InputMaybe<TdaFisherTeamResultFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeets_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetsArgs = {
  filters?: InputMaybe<TdaRezfishMeetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetCategoryArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetCategories_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetCategoriesArgs = {
  filters?: InputMaybe<TdaRezfishMeetCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetLengthArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetLengths_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMeetLengthFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMeetLengthsArgs = {
  filters?: InputMaybe<TdaRezfishMeetLengthFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMentorArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMentors_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishMentorsArgs = {
  filters?: InputMaybe<TdaRezfishMentorFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishPlatformArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishPlatforms_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishPlatformFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishPlatformsArgs = {
  filters?: InputMaybe<TdaRezfishPlatformFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishTagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishTags_ConnectionArgs = {
  filters?: InputMaybe<TdaRezfishTagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaRezfishTagsArgs = {
  filters?: InputMaybe<TdaRezfishTagFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationGroups_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationGroupsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationQuestionArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationQuestions_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationQuestionsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationQuestionFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationSetArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationSets_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationSetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationSetsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationSetFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationTeamArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationTeams_ConnectionArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaSpechfishEvaluationTeamsArgs = {
  filters?: InputMaybe<TdaSpechfishEvaluationTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaTdcApplicationArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaTdcApplications_ConnectionArgs = {
  filters?: InputMaybe<TdaTdcApplicationFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryTdaTdcApplicationsArgs = {
  filters?: InputMaybe<TdaTdcApplicationFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryWebArticleArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticles_ConnectionArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticlesArgs = {
  filters?: InputMaybe<WebArticleFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticleCategoryArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticleCategories_ConnectionArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebArticleCategoriesArgs = {
  filters?: InputMaybe<WebArticleCategoryFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContacts_ConnectionArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactsArgs = {
  filters?: InputMaybe<WebContactFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactGroups_ConnectionArgs = {
  filters?: InputMaybe<WebContactGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebContactGroupsArgs = {
  filters?: InputMaybe<WebContactGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebMenuItemArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebMenuItems_ConnectionArgs = {
  filters?: InputMaybe<WebMenuItemFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebMenuItemsArgs = {
  filters?: InputMaybe<WebMenuItemFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPageArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPages_ConnectionArgs = {
  filters?: InputMaybe<WebPageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPagesArgs = {
  filters?: InputMaybe<WebPageFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnerArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartners_ConnectionArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnersArgs = {
  filters?: InputMaybe<WebPartnerFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnerGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnerGroups_ConnectionArgs = {
  filters?: InputMaybe<WebPartnerGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryWebPartnerGroupsArgs = {
  filters?: InputMaybe<WebPartnerGroupFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type QueryXoGhostProjectArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostProjects_ConnectionArgs = {
  filters?: InputMaybe<XoGhostProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostProjectsArgs = {
  filters?: InputMaybe<XoGhostProjectFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostSchoolArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostSchools_ConnectionArgs = {
  filters?: InputMaybe<XoGhostSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostSchoolsArgs = {
  filters?: InputMaybe<XoGhostSchoolFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostTeamArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostTeams_ConnectionArgs = {
  filters?: InputMaybe<XoGhostTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostTeamsArgs = {
  filters?: InputMaybe<XoGhostTeamFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostTournamentArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostTournaments_ConnectionArgs = {
  filters?: InputMaybe<XoGhostTournamentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};


export type QueryXoGhostTournamentsArgs = {
  filters?: InputMaybe<XoGhostTournamentFiltersInput>;
  pagination?: InputMaybe<PaginationArg>;
  sort?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  status?: InputMaybe<PublicationStatus>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createReviewWorkflowsWorkflow?: Maybe<ReviewWorkflowsWorkflow>;
  updateReviewWorkflowsWorkflow?: Maybe<ReviewWorkflowsWorkflow>;
  deleteReviewWorkflowsWorkflow?: Maybe<DeleteMutationResponse>;
  createReviewWorkflowsWorkflowStage?: Maybe<ReviewWorkflowsWorkflowStage>;
  updateReviewWorkflowsWorkflowStage?: Maybe<ReviewWorkflowsWorkflowStage>;
  deleteReviewWorkflowsWorkflowStage?: Maybe<DeleteMutationResponse>;
  createStrapiPluginSsoRoles?: Maybe<StrapiPluginSsoRoles>;
  updateStrapiPluginSsoRoles?: Maybe<StrapiPluginSsoRoles>;
  deleteStrapiPluginSsoRoles?: Maybe<DeleteMutationResponse>;
  createStrapiPluginSsoWhitelists?: Maybe<StrapiPluginSsoWhitelists>;
  updateStrapiPluginSsoWhitelists?: Maybe<StrapiPluginSsoWhitelists>;
  deleteStrapiPluginSsoWhitelists?: Maybe<DeleteMutationResponse>;
  createCoreContestant?: Maybe<CoreContestant>;
  updateCoreContestant?: Maybe<CoreContestant>;
  deleteCoreContestant?: Maybe<DeleteMutationResponse>;
  createCoreEvent?: Maybe<CoreEvent>;
  updateCoreEvent?: Maybe<CoreEvent>;
  deleteCoreEvent?: Maybe<DeleteMutationResponse>;
  createCoreEventGroup?: Maybe<CoreEventGroup>;
  updateCoreEventGroup?: Maybe<CoreEventGroup>;
  deleteCoreEventGroup?: Maybe<DeleteMutationResponse>;
  createCoreProject?: Maybe<CoreProject>;
  updateCoreProject?: Maybe<CoreProject>;
  deleteCoreProject?: Maybe<DeleteMutationResponse>;
  createCoreProjectType?: Maybe<CoreProjectType>;
  updateCoreProjectType?: Maybe<CoreProjectType>;
  deleteCoreProjectType?: Maybe<DeleteMutationResponse>;
  createCoreRegion?: Maybe<CoreRegion>;
  updateCoreRegion?: Maybe<CoreRegion>;
  deleteCoreRegion?: Maybe<DeleteMutationResponse>;
  createCoreSchool?: Maybe<CoreSchool>;
  updateCoreSchool?: Maybe<CoreSchool>;
  deleteCoreSchool?: Maybe<DeleteMutationResponse>;
  createCoreSchoolContact?: Maybe<CoreSchoolContact>;
  updateCoreSchoolContact?: Maybe<CoreSchoolContact>;
  deleteCoreSchoolContact?: Maybe<DeleteMutationResponse>;
  createCoreSchoolType?: Maybe<CoreSchoolType>;
  updateCoreSchoolType?: Maybe<CoreSchoolType>;
  deleteCoreSchoolType?: Maybe<DeleteMutationResponse>;
  createDummy?: Maybe<Dummy>;
  updateDummy?: Maybe<Dummy>;
  deleteDummy?: Maybe<DeleteMutationResponse>;
  createMailingBatch?: Maybe<MailingBatch>;
  updateMailingBatch?: Maybe<MailingBatch>;
  deleteMailingBatch?: Maybe<DeleteMutationResponse>;
  updateMailingBlacklist?: Maybe<MailingBlacklist>;
  deleteMailingBlacklist?: Maybe<DeleteMutationResponse>;
  createMailingSource?: Maybe<MailingSource>;
  updateMailingSource?: Maybe<MailingSource>;
  deleteMailingSource?: Maybe<DeleteMutationResponse>;
  createMailingTemplate?: Maybe<MailingTemplate>;
  updateMailingTemplate?: Maybe<MailingTemplate>;
  deleteMailingTemplate?: Maybe<DeleteMutationResponse>;
  createMailingTheme?: Maybe<MailingTheme>;
  updateMailingTheme?: Maybe<MailingTheme>;
  deleteMailingTheme?: Maybe<DeleteMutationResponse>;
  updateScgBotConfig?: Maybe<ScgBotConfig>;
  deleteScgBotConfig?: Maybe<DeleteMutationResponse>;
  createScgBotSuggestion?: Maybe<ScgBotSuggestion>;
  updateScgBotSuggestion?: Maybe<ScgBotSuggestion>;
  deleteScgBotSuggestion?: Maybe<DeleteMutationResponse>;
  updateTdaBot?: Maybe<TdaBot>;
  deleteTdaBot?: Maybe<DeleteMutationResponse>;
  createTdaBotContestant?: Maybe<TdaBotContestant>;
  updateTdaBotContestant?: Maybe<TdaBotContestant>;
  deleteTdaBotContestant?: Maybe<DeleteMutationResponse>;
  createTdaBotTag?: Maybe<TdaBotTag>;
  updateTdaBotTag?: Maybe<TdaBotTag>;
  deleteTdaBotTag?: Maybe<DeleteMutationResponse>;
  createTdaBusherTeamTest?: Maybe<TdaBusherTeamTest>;
  updateTdaBusherTeamTest?: Maybe<TdaBusherTeamTest>;
  deleteTdaBusherTeamTest?: Maybe<DeleteMutationResponse>;
  createTdaBusherTest?: Maybe<TdaBusherTest>;
  updateTdaBusherTest?: Maybe<TdaBusherTest>;
  deleteTdaBusherTest?: Maybe<DeleteMutationResponse>;
  createTdaCorePhase?: Maybe<TdaCorePhase>;
  updateTdaCorePhase?: Maybe<TdaCorePhase>;
  deleteTdaCorePhase?: Maybe<DeleteMutationResponse>;
  createTdaCoreTeam?: Maybe<TdaCoreTeam>;
  updateTdaCoreTeam?: Maybe<TdaCoreTeam>;
  deleteTdaCoreTeam?: Maybe<DeleteMutationResponse>;
  createTdaFisherFlag?: Maybe<TdaFisherFlag>;
  updateTdaFisherFlag?: Maybe<TdaFisherFlag>;
  deleteTdaFisherFlag?: Maybe<DeleteMutationResponse>;
  createTdaFisherTeamFlag?: Maybe<TdaFisherTeamFlag>;
  updateTdaFisherTeamFlag?: Maybe<TdaFisherTeamFlag>;
  deleteTdaFisherTeamFlag?: Maybe<DeleteMutationResponse>;
  createTdaFisherTeamResult?: Maybe<TdaFisherTeamResult>;
  updateTdaFisherTeamResult?: Maybe<TdaFisherTeamResult>;
  deleteTdaFisherTeamResult?: Maybe<DeleteMutationResponse>;
  createTdaRezfishMeet?: Maybe<TdaRezfishMeet>;
  updateTdaRezfishMeet?: Maybe<TdaRezfishMeet>;
  deleteTdaRezfishMeet?: Maybe<DeleteMutationResponse>;
  createTdaRezfishMeetCategory?: Maybe<TdaRezfishMeetCategory>;
  updateTdaRezfishMeetCategory?: Maybe<TdaRezfishMeetCategory>;
  deleteTdaRezfishMeetCategory?: Maybe<DeleteMutationResponse>;
  createTdaRezfishMeetLength?: Maybe<TdaRezfishMeetLength>;
  updateTdaRezfishMeetLength?: Maybe<TdaRezfishMeetLength>;
  deleteTdaRezfishMeetLength?: Maybe<DeleteMutationResponse>;
  createTdaRezfishMentor?: Maybe<TdaRezfishMentor>;
  updateTdaRezfishMentor?: Maybe<TdaRezfishMentor>;
  deleteTdaRezfishMentor?: Maybe<DeleteMutationResponse>;
  createTdaRezfishPlatform?: Maybe<TdaRezfishPlatform>;
  updateTdaRezfishPlatform?: Maybe<TdaRezfishPlatform>;
  deleteTdaRezfishPlatform?: Maybe<DeleteMutationResponse>;
  createTdaRezfishTag?: Maybe<TdaRezfishTag>;
  updateTdaRezfishTag?: Maybe<TdaRezfishTag>;
  deleteTdaRezfishTag?: Maybe<DeleteMutationResponse>;
  createTdaSpechfishEvaluationGroup?: Maybe<TdaSpechfishEvaluationGroup>;
  updateTdaSpechfishEvaluationGroup?: Maybe<TdaSpechfishEvaluationGroup>;
  deleteTdaSpechfishEvaluationGroup?: Maybe<DeleteMutationResponse>;
  createTdaSpechfishEvaluationQuestion?: Maybe<TdaSpechfishEvaluationQuestion>;
  updateTdaSpechfishEvaluationQuestion?: Maybe<TdaSpechfishEvaluationQuestion>;
  deleteTdaSpechfishEvaluationQuestion?: Maybe<DeleteMutationResponse>;
  createTdaSpechfishEvaluationSet?: Maybe<TdaSpechfishEvaluationSet>;
  updateTdaSpechfishEvaluationSet?: Maybe<TdaSpechfishEvaluationSet>;
  deleteTdaSpechfishEvaluationSet?: Maybe<DeleteMutationResponse>;
  createTdaSpechfishEvaluationTeam?: Maybe<TdaSpechfishEvaluationTeam>;
  updateTdaSpechfishEvaluationTeam?: Maybe<TdaSpechfishEvaluationTeam>;
  deleteTdaSpechfishEvaluationTeam?: Maybe<DeleteMutationResponse>;
  createTdaTdcApplication?: Maybe<TdaTdcApplication>;
  updateTdaTdcApplication?: Maybe<TdaTdcApplication>;
  deleteTdaTdcApplication?: Maybe<DeleteMutationResponse>;
  createWebArticle?: Maybe<WebArticle>;
  updateWebArticle?: Maybe<WebArticle>;
  deleteWebArticle?: Maybe<DeleteMutationResponse>;
  createWebArticleCategory?: Maybe<WebArticleCategory>;
  updateWebArticleCategory?: Maybe<WebArticleCategory>;
  deleteWebArticleCategory?: Maybe<DeleteMutationResponse>;
  createWebContact?: Maybe<WebContact>;
  updateWebContact?: Maybe<WebContact>;
  deleteWebContact?: Maybe<DeleteMutationResponse>;
  createWebContactGroup?: Maybe<WebContactGroup>;
  updateWebContactGroup?: Maybe<WebContactGroup>;
  deleteWebContactGroup?: Maybe<DeleteMutationResponse>;
  createWebMenuItem?: Maybe<WebMenuItem>;
  updateWebMenuItem?: Maybe<WebMenuItem>;
  deleteWebMenuItem?: Maybe<DeleteMutationResponse>;
  createWebPage?: Maybe<WebPage>;
  updateWebPage?: Maybe<WebPage>;
  deleteWebPage?: Maybe<DeleteMutationResponse>;
  createWebPartner?: Maybe<WebPartner>;
  updateWebPartner?: Maybe<WebPartner>;
  deleteWebPartner?: Maybe<DeleteMutationResponse>;
  createWebPartnerGroup?: Maybe<WebPartnerGroup>;
  updateWebPartnerGroup?: Maybe<WebPartnerGroup>;
  deleteWebPartnerGroup?: Maybe<DeleteMutationResponse>;
  createXoGhostProject?: Maybe<XoGhostProject>;
  updateXoGhostProject?: Maybe<XoGhostProject>;
  deleteXoGhostProject?: Maybe<DeleteMutationResponse>;
  createXoGhostSchool?: Maybe<XoGhostSchool>;
  updateXoGhostSchool?: Maybe<XoGhostSchool>;
  deleteXoGhostSchool?: Maybe<DeleteMutationResponse>;
  createXoGhostTeam?: Maybe<XoGhostTeam>;
  updateXoGhostTeam?: Maybe<XoGhostTeam>;
  deleteXoGhostTeam?: Maybe<DeleteMutationResponse>;
  createXoGhostTournament?: Maybe<XoGhostTournament>;
  updateXoGhostTournament?: Maybe<XoGhostTournament>;
  deleteXoGhostTournament?: Maybe<DeleteMutationResponse>;
  updateUploadFile: UploadFile;
  deleteUploadFile?: Maybe<UploadFile>;
  /** Create a new role */
  createUsersPermissionsRole?: Maybe<UsersPermissionsCreateRolePayload>;
  /** Update an existing role */
  updateUsersPermissionsRole?: Maybe<UsersPermissionsUpdateRolePayload>;
  /** Delete an existing role */
  deleteUsersPermissionsRole?: Maybe<UsersPermissionsDeleteRolePayload>;
  /** Create a new user */
  createUsersPermissionsUser: UsersPermissionsUserEntityResponse;
  /** Update an existing user */
  updateUsersPermissionsUser: UsersPermissionsUserEntityResponse;
  /** Delete an existing user */
  deleteUsersPermissionsUser: UsersPermissionsUserEntityResponse;
  login: UsersPermissionsLoginPayload;
  /** Register a user */
  register: UsersPermissionsLoginPayload;
  /** Request a reset password token */
  forgotPassword?: Maybe<UsersPermissionsPasswordPayload>;
  /** Reset user password. Confirm with a code (resetToken from forgotPassword) */
  resetPassword?: Maybe<UsersPermissionsLoginPayload>;
  /** Change user password. Confirm with the current password. */
  changePassword?: Maybe<UsersPermissionsLoginPayload>;
  /** Confirm an email users email address */
  emailConfirmation?: Maybe<UsersPermissionsLoginPayload>;
};


export type MutationCreateReviewWorkflowsWorkflowArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: ReviewWorkflowsWorkflowInput;
};


export type MutationUpdateReviewWorkflowsWorkflowArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: ReviewWorkflowsWorkflowInput;
};


export type MutationDeleteReviewWorkflowsWorkflowArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateReviewWorkflowsWorkflowStageArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: ReviewWorkflowsWorkflowStageInput;
};


export type MutationUpdateReviewWorkflowsWorkflowStageArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: ReviewWorkflowsWorkflowStageInput;
};


export type MutationDeleteReviewWorkflowsWorkflowStageArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateStrapiPluginSsoRolesArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: StrapiPluginSsoRolesInput;
};


export type MutationUpdateStrapiPluginSsoRolesArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: StrapiPluginSsoRolesInput;
};


export type MutationDeleteStrapiPluginSsoRolesArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateStrapiPluginSsoWhitelistsArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: StrapiPluginSsoWhitelistsInput;
};


export type MutationUpdateStrapiPluginSsoWhitelistsArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: StrapiPluginSsoWhitelistsInput;
};


export type MutationDeleteStrapiPluginSsoWhitelistsArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreContestantArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreContestantInput;
};


export type MutationUpdateCoreContestantArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreContestantInput;
};


export type MutationDeleteCoreContestantArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreEventArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreEventInput;
};


export type MutationUpdateCoreEventArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreEventInput;
};


export type MutationDeleteCoreEventArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreEventGroupArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreEventGroupInput;
};


export type MutationUpdateCoreEventGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreEventGroupInput;
};


export type MutationDeleteCoreEventGroupArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreProjectArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreProjectInput;
};


export type MutationUpdateCoreProjectArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreProjectInput;
};


export type MutationDeleteCoreProjectArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreProjectTypeArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreProjectTypeInput;
};


export type MutationUpdateCoreProjectTypeArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreProjectTypeInput;
};


export type MutationDeleteCoreProjectTypeArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreRegionArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreRegionInput;
};


export type MutationUpdateCoreRegionArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreRegionInput;
};


export type MutationDeleteCoreRegionArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreSchoolArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreSchoolInput;
};


export type MutationUpdateCoreSchoolArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreSchoolInput;
};


export type MutationDeleteCoreSchoolArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreSchoolContactArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreSchoolContactInput;
};


export type MutationUpdateCoreSchoolContactArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreSchoolContactInput;
};


export type MutationDeleteCoreSchoolContactArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateCoreSchoolTypeArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: CoreSchoolTypeInput;
};


export type MutationUpdateCoreSchoolTypeArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: CoreSchoolTypeInput;
};


export type MutationDeleteCoreSchoolTypeArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateDummyArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: DummyInput;
};


export type MutationUpdateDummyArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: DummyInput;
};


export type MutationDeleteDummyArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateMailingBatchArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: MailingBatchInput;
};


export type MutationUpdateMailingBatchArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: MailingBatchInput;
};


export type MutationDeleteMailingBatchArgs = {
  documentId: Scalars['ID'];
};


export type MutationUpdateMailingBlacklistArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: MailingBlacklistInput;
};


export type MutationCreateMailingSourceArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: MailingSourceInput;
};


export type MutationUpdateMailingSourceArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: MailingSourceInput;
};


export type MutationDeleteMailingSourceArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateMailingTemplateArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: MailingTemplateInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateMailingTemplateArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: MailingTemplateInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteMailingTemplateArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateMailingThemeArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: MailingThemeInput;
};


export type MutationUpdateMailingThemeArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: MailingThemeInput;
};


export type MutationDeleteMailingThemeArgs = {
  documentId: Scalars['ID'];
};


export type MutationUpdateScgBotConfigArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: ScgBotConfigInput;
};


export type MutationCreateScgBotSuggestionArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: ScgBotSuggestionInput;
};


export type MutationUpdateScgBotSuggestionArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: ScgBotSuggestionInput;
};


export type MutationDeleteScgBotSuggestionArgs = {
  documentId: Scalars['ID'];
};


export type MutationUpdateTdaBotArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaBotInput;
};


export type MutationCreateTdaBotContestantArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaBotContestantInput;
};


export type MutationUpdateTdaBotContestantArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaBotContestantInput;
};


export type MutationDeleteTdaBotContestantArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaBotTagArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaBotTagInput;
};


export type MutationUpdateTdaBotTagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaBotTagInput;
};


export type MutationDeleteTdaBotTagArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaBusherTeamTestArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaBusherTeamTestInput;
};


export type MutationUpdateTdaBusherTeamTestArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaBusherTeamTestInput;
};


export type MutationDeleteTdaBusherTeamTestArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaBusherTestArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaBusherTestInput;
};


export type MutationUpdateTdaBusherTestArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaBusherTestInput;
};


export type MutationDeleteTdaBusherTestArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaCorePhaseArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaCorePhaseInput;
};


export type MutationUpdateTdaCorePhaseArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaCorePhaseInput;
};


export type MutationDeleteTdaCorePhaseArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaCoreTeamArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaCoreTeamInput;
};


export type MutationUpdateTdaCoreTeamArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaCoreTeamInput;
};


export type MutationDeleteTdaCoreTeamArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaFisherFlagArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaFisherFlagInput;
};


export type MutationUpdateTdaFisherFlagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaFisherFlagInput;
};


export type MutationDeleteTdaFisherFlagArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaFisherTeamFlagArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaFisherTeamFlagInput;
};


export type MutationUpdateTdaFisherTeamFlagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaFisherTeamFlagInput;
};


export type MutationDeleteTdaFisherTeamFlagArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaFisherTeamResultArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaFisherTeamResultInput;
};


export type MutationUpdateTdaFisherTeamResultArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaFisherTeamResultInput;
};


export type MutationDeleteTdaFisherTeamResultArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaRezfishMeetArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMeetInput;
};


export type MutationUpdateTdaRezfishMeetArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMeetInput;
};


export type MutationDeleteTdaRezfishMeetArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaRezfishMeetCategoryArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMeetCategoryInput;
};


export type MutationUpdateTdaRezfishMeetCategoryArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMeetCategoryInput;
};


export type MutationDeleteTdaRezfishMeetCategoryArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaRezfishMeetLengthArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMeetLengthInput;
};


export type MutationUpdateTdaRezfishMeetLengthArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMeetLengthInput;
};


export type MutationDeleteTdaRezfishMeetLengthArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaRezfishMentorArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMentorInput;
};


export type MutationUpdateTdaRezfishMentorArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishMentorInput;
};


export type MutationDeleteTdaRezfishMentorArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaRezfishPlatformArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishPlatformInput;
};


export type MutationUpdateTdaRezfishPlatformArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishPlatformInput;
};


export type MutationDeleteTdaRezfishPlatformArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaRezfishTagArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishTagInput;
};


export type MutationUpdateTdaRezfishTagArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaRezfishTagInput;
};


export type MutationDeleteTdaRezfishTagArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaSpechfishEvaluationGroupArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationGroupInput;
};


export type MutationUpdateTdaSpechfishEvaluationGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationGroupInput;
};


export type MutationDeleteTdaSpechfishEvaluationGroupArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaSpechfishEvaluationQuestionArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationQuestionInput;
};


export type MutationUpdateTdaSpechfishEvaluationQuestionArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationQuestionInput;
};


export type MutationDeleteTdaSpechfishEvaluationQuestionArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaSpechfishEvaluationSetArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationSetInput;
};


export type MutationUpdateTdaSpechfishEvaluationSetArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationSetInput;
};


export type MutationDeleteTdaSpechfishEvaluationSetArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaSpechfishEvaluationTeamArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationTeamInput;
};


export type MutationUpdateTdaSpechfishEvaluationTeamArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaSpechfishEvaluationTeamInput;
};


export type MutationDeleteTdaSpechfishEvaluationTeamArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateTdaTdcApplicationArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: TdaTdcApplicationInput;
};


export type MutationUpdateTdaTdcApplicationArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: TdaTdcApplicationInput;
};


export type MutationDeleteTdaTdcApplicationArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateWebArticleArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebArticleInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebArticleArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebArticleInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebArticleArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebArticleCategoryArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebArticleCategoryInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebArticleCategoryArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebArticleCategoryInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebArticleCategoryArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebContactArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebContactInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebContactArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebContactInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebContactArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebContactGroupArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebContactGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebContactGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebContactGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebContactGroupArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebMenuItemArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebMenuItemInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebMenuItemArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebMenuItemInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebMenuItemArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPageArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebPageInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebPageArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebPageInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebPageArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPartnerArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebPartnerInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebPartnerArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebPartnerInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebPartnerArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateWebPartnerGroupArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: WebPartnerGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationUpdateWebPartnerGroupArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: WebPartnerGroupInput;
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationDeleteWebPartnerGroupArgs = {
  documentId: Scalars['ID'];
  locale?: InputMaybe<Scalars['I18NLocaleCode']>;
};


export type MutationCreateXoGhostProjectArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostProjectInput;
};


export type MutationUpdateXoGhostProjectArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostProjectInput;
};


export type MutationDeleteXoGhostProjectArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateXoGhostSchoolArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostSchoolInput;
};


export type MutationUpdateXoGhostSchoolArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostSchoolInput;
};


export type MutationDeleteXoGhostSchoolArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateXoGhostTeamArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostTeamInput;
};


export type MutationUpdateXoGhostTeamArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostTeamInput;
};


export type MutationDeleteXoGhostTeamArgs = {
  documentId: Scalars['ID'];
};


export type MutationCreateXoGhostTournamentArgs = {
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostTournamentInput;
};


export type MutationUpdateXoGhostTournamentArgs = {
  documentId: Scalars['ID'];
  status?: InputMaybe<PublicationStatus>;
  data: XoGhostTournamentInput;
};


export type MutationDeleteXoGhostTournamentArgs = {
  documentId: Scalars['ID'];
};


export type MutationUpdateUploadFileArgs = {
  id: Scalars['ID'];
  info?: InputMaybe<FileInfoInput>;
};


export type MutationDeleteUploadFileArgs = {
  id: Scalars['ID'];
};


export type MutationCreateUsersPermissionsRoleArgs = {
  data: UsersPermissionsRoleInput;
};


export type MutationUpdateUsersPermissionsRoleArgs = {
  id: Scalars['ID'];
  data: UsersPermissionsRoleInput;
};


export type MutationDeleteUsersPermissionsRoleArgs = {
  id: Scalars['ID'];
};


export type MutationCreateUsersPermissionsUserArgs = {
  data: UsersPermissionsUserInput;
};


export type MutationUpdateUsersPermissionsUserArgs = {
  id: Scalars['ID'];
  data: UsersPermissionsUserInput;
};


export type MutationDeleteUsersPermissionsUserArgs = {
  id: Scalars['ID'];
};


export type MutationLoginArgs = {
  input: UsersPermissionsLoginInput;
};


export type MutationRegisterArgs = {
  input: UsersPermissionsRegisterInput;
};


export type MutationForgotPasswordArgs = {
  email: Scalars['String'];
};


export type MutationResetPasswordArgs = {
  password: Scalars['String'];
  passwordConfirmation: Scalars['String'];
  code: Scalars['String'];
};


export type MutationChangePasswordArgs = {
  currentPassword: Scalars['String'];
  password: Scalars['String'];
  passwordConfirmation: Scalars['String'];
};


export type MutationEmailConfirmationArgs = {
  confirmation: Scalars['String'];
};
